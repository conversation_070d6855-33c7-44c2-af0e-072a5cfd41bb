#!/bin/sh

if [ -t 0 ] && [ -e /dev/tty ]; then
    exec < /dev/tty

    $SHELL -i -c "cd '$(pwd)' && nvm use 22 && npm run build:prod --project respondo-team" || { echo "❌ Błąd budowania projektu (interactive): respondo-team"; exit 1; }
    $SHELL -i -c "cd '$(pwd)' && nvm use 22 && npm run build:prod --project respondo-client" || { echo "❌ Błąd budowania projektu (interactive): respondo-client"; exit 1; }
else
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"

    nvm use 22 || { echo "❌ Node v22 jest wymagany!"; exit 1; }

    cd "$(pwd)" || exit 1

    npm run build:prod --project respondo-team || { echo "❌ Błąd budowania projektu: respondo-team"; exit 1; }
    npm run build:prod --project respondo-client || { echo "❌ Błąd budowania projektu: respondo-client"; exit 1; }
fi

echo "✅ Budowanie zakończone sukcesem."