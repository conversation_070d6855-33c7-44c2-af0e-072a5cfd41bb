import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {AuthService} from '../../services/auth/auth.service';
import {Title} from '@angular/platform-browser';
import {environment} from '../../../environments/environment';
import { MatMenuItem } from '@angular/material/menu';
import { MatIcon } from '@angular/material/icon';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-logout',
    templateUrl: './logout.component.html',
    styleUrls: ['./logout.component.scss'],
    imports: [MatMenuItem, MatIcon, TranslatePipe]
})
export class LogoutComponent implements OnInit {

    constructor(private router: Router, private authService: AuthService, private titleService: Title) {
    }

    ngOnInit() {
    }

    logout() {
        this.authService.logout();
        this.titleService.setTitle(environment['appTitle']);
    }
}
