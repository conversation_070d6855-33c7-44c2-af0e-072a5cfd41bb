import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NotificationData, NotificationInterface} from '../../../common/interfaces/notification.interface';
import {UserStoreService} from '../../../services/store/user-store.service';
import { RouterLink } from '@angular/router';
import { NgSwitch, NgSwitchCase, NgSwitchDefault } from '@angular/common';
import { DateDiffFromNowPipe } from '../../../shared/pipes/date-diff-from-now.pipe';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-notification-item',
    templateUrl: './notification-item.component.html',
    styleUrls: ['./notification-item.component.scss'],
    imports: [RouterLink, NgSwitch, NgSwitchCase, NgSwitchDefault, DateDiffFromNowPipe, TranslatePipe]
})
export class NotificationItemComponent implements OnInit {

    @Input()
    notificationData: NotificationInterface;

    @Output()
    notificationRead: EventEmitter<number> = new EventEmitter();

    notificationCustomData: NotificationData;

    userData;

    objectLink: string;

    constructor(private userStoreService: UserStoreService) {
    }

    ngOnInit() {
        if (this.notificationData.type === 'comment_add' || this.notificationData.type === 'comment_reply') {
            this.userStoreService.getUserFromStoreTake1(this.notificationData.from_user_id).subscribe(
                ({User}) => this.userData = User
            );
        }

        if (this.notificationData.data) {
            this.notificationCustomData = JSON.parse(this.notificationData.data) as NotificationData;
        }

        this.createObjectLink()
    }

    private createObjectLink() {
        switch (this.notificationData.type) {
            case 'mail_received_success':
            case 'mail_received_error':
                this.objectLink = '/mail/important';

                break;
            case 'mail_sent_error':
            case 'mail_sent_success':
                this.objectLink = '/mail/sent';

                break;
            case 'comment_add':
            case 'comment_reply':
            case 'issue_payment':
            case 'issue_rejection':
            case 'issue_complaint':
            case 'invoice_failed':
            case 'reply_rejected':
                this.objectLink = '/issue/' + this.notificationData.issue_id;

                break;
            default:
                this.objectLink = '/start';
        }
    }

    onClick() {
        this.notificationRead.emit(this.notificationData.id);
    }
}
