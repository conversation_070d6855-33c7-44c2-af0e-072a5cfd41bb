<a class="notification-item" [routerLink]="objectLink" (click)="onClick()">
    <div class="notification-content">
        <div class="notification-message">
            <ng-container [ngSwitch]="notificationData.type">
                <span *ngSwitchCase="'comment_add'" class="text-wrap">
                    {{'NOTIFICATION.WORKER' | translate}} <b>{{userData?.lastname}} {{userData?.firstname}}</b>
                    {{'NOTIFICATION.ADD-COMMENT' | translate}} <b>{{notificationData.issue_internal_id}}</b>.
                </span>

                <span *ngSwitchCase="'comment_reply'" class="text-wrap">
                    {{'NOTIFICATION.WORKER' | translate}} <b>{{userData?.lastname}} {{userData?.firstname}}</b> {{'NOTIFICATION.REPLIED-COMMENT' | translate}} <b>{{notificationData.issue_internal_id}}</b>.
                </span>

                <span *ngSwitchCase="'reply_rejected'" class="text-wrap">
                    {{'NOTIFICATION.YOUR-RESPONSE' | translate}} <b>{{notificationData.issue_internal_id}}</b> {{'NOTIFICATION.REJECT' | translate}}
                </span>

                <span *ngSwitchCase="'issue_payment'" class="text-wrap">
                    {{'NOTIFICATION.ISSUE-NUMBER' | translate}} <b>{{notificationData.issue_internal_id}}</b> {{'NOTIFICATION.PAID' | translate}}
                </span>

                <span *ngSwitchCase="'issue_rejection'" class="text-wrap">
                    {{'NOTIFICATION.ISSUE-NUMBER' | translate}} <b>{{notificationData.issue_internal_id}}</b> {{'NOTIFICATION.ISSUE-CANCEL' | translate}}
                </span>

                <span *ngSwitchCase="'issue_complaint'" class="text-wrap">
                    {{'NOTIFICATION.ISSUE-NUMBER' | translate}} <b>{{notificationData.issue_internal_id}}</b> {{'NOTIFICATION.ISSUE-COMPLAIN' | translate}}
                </span>

                <span *ngSwitchCase="'invoice_failed'" class="text-wrap">
                    {{'NOTIFICATION.INVOICE-FAILED' | translate}} <b>{{notificationData.issue_internal_id}}</b>.
                </span>

                <span *ngSwitchCase="'mail_received_success'" class="text-wrap">
                    <ng-container [ngSwitch]="+notificationCustomData?.counter">
                          <ng-container *ngSwitchCase="0">
                            {{ 'NOTIFICATION.MAIL_RECEIVED_SUCCESS' | translate }}
                          </ng-container>

                          <ng-container *ngSwitchCase="1">
                            {{ 'NOTIFICATION.MAIL_RECEIVED_SUCCESS_ONE' | translate: {counter: +notificationCustomData?.counter} }}
                          </ng-container>

                          <ng-container *ngSwitchDefault>
                            {{ 'NOTIFICATION.MAIL_RECEIVED_SUCCESS_MANY' | translate: {counter: +notificationCustomData?.counter} }}
                          </ng-container>
                    </ng-container>
                </span>

                <span *ngSwitchCase="'mail_received_error'" class="text-wrap">
                    {{ 'NOTIFICATION.MAIL_RECEIVED_ERROR' | translate }}
                </span>

                <span *ngSwitchCase="'mail_sent_success'" class="text-wrap">
                    {{ 'NOTIFICATION.MAIL_SENT_SUCCESS' | translate }}
                </span>

                <span *ngSwitchCase="'mail_sent_error'" class="text-wrap">
                    {{ 'NOTIFICATION.MAIL_SENT_ERROR' | translate: {email: notificationCustomData?.mail_address} }}
                </span>
            </ng-container>
        </div>
        <div class="notification-time">{{notificationData.created | dateDiffFromNow}} temu</div>
    </div>
</a>
