@use '../../../variables' as *;

mat-sidenav-container {
    height: 100%;

    .mat-sidenav-content {
        overflow: visible;
        height: 100%;
        margin: 0 0 0 82px;
        background-color: $light-grey;

        &.is-expanded-left {
            margin-left: 265px;
        }

        &.is-expanded-right {
            margin-right: 300px !important;

            ::ng-deep.mat-toolbar.header {
                transform: translateX(299px) !important;
            }
        }

        &.without-users-sidebar {
            margin: 0 0 0 64px !important;

            &.is-expanded-left {
                margin-left: 275px !important;
            }
        }
    }

    &.is-mobile .mat-sidenav-content {
        margin: 0 !important;

        height: calc(100% - 56px);
    }
}

.users-sidebar {
    border: 0;
    border-radius: 10px 0 0 0;
    margin-top: 64px;
}

mat-sidenav {
    border: 0;
    width: 0;

    &.mat-drawer-opened {
        width: 80px;

        @media (min-width: 960px) {
            transition: width .3s;
        }
    }

    &.is-expanded {
        width: 264px;

        &.users-sidebar {
            width: 300px;
        }
    }

    &.is-blurred {
        background: rgba(0,0,0, .6);
        opacity: 0.5;
        pointer-events: none;
    }
}
