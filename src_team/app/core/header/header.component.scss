@use "../../../variables" as *;

.header {
    &-tour {
        color: $fiveways-button-border;
        position: relative;
        bottom: 3px;
        left: 1px;
    }

    &-element {
        &-websockets {
            color: $fiveways-button-border
        }
    }

    &-sockets {
        display: flex;
        justify-content: center;
    }

    &-avatar {
        padding: 10px 15px 17px 10px;
    }

    &-burger {
        color: $fiveways-button-border
    }
}

.mat-toolbar {
    background-color: $fiveways-background-left;
    height: 64px;

    &.without-users-sidebar {
        @media (min-width: 960px) {
            transform: none;
        }
    }

    .header {
        &__items {
            display: flex;
            width: 100%;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            gap: 16px;
        }
    }
}

.avatar {
    border-radius: 50%;
    cursor: pointer;
    height: 40px;
    width: 40px;
}

.settings-avatar {
    cursor: pointer;
}

.toggle-users-sidebar {
    //margin-left: 20px;
}

.start-tour {
    border-radius: 0;
    display: flex;
    justify-content: center;
    line-height: 32px;

    &:hover {
        background-color: $hover;
        border-radius: 8px;
    }
}

.svg-icon {
    height: 20px;
    vertical-align: sub;
    width: 25px;
}

.header-divider {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid var(--background-color);
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
    width: calc(100% - 30px);
}

.is-not-mobile {
    @media(min-width: 960px) {
        display: none;
    }
}
