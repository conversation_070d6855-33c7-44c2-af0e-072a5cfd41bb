<mat-toolbar
    role="heading"
    class="header"
    color="primary"
    [ngClass]="{'without-users-sidebar': withoutUsersSidebar}"
    (click)="onClick($event)">

    <div class="header__items">
        <ng-container *ngIf="isMobileService.isHandsetOrTablet | async">
            <button
                    class="is-not-mobile"
                    type="button"
                    aria-label="Toggle sidenav"
                    mat-icon-button
                    (click)="menuSidebarToggle()">
                <mat-icon class="header-burger" aria-label="Side nav toggle icon">menu</mat-icon>
            </button>
        </ng-container>
        <span class="flex"></span>

        <div *ngIf="isAdmin" class="header-element">
            <app-relogin></app-relogin>
        </div>

        <div *ngIf="!(socketsService.connected$ | async)" class="header-element header-sockets">
            <mat-icon
                    class="header-element-websockets"
                    matTooltip="{{'HEADER.WEB-SOCKET-ERROR' | translate}}"
            >
                offline_bolt
            </mat-icon>
        </div>

        <button mat-icon-button class="start-tour header-element" (click)="startTour()" tourAnchor="header.tour">
            <i-lucide name="circle-help" class="header-tour" matTooltip="{{'HEADER.TOUR' | translate}}"></i-lucide>
        </button>

        <app-notification-menu class="header-element"></app-notification-menu>

        <app-user-avatar-display
                class="settings-avatar header-element header-avatar"
                tourAnchor="header.avatar"
                [userId]="actualUserId"
                [showChevron]="true"
                [matMenuTriggerFor]="menu">
        </app-user-avatar-display>

        <mat-menu #menu="matMenu" yPosition="below">
            <ng-container>
                <app-sso [actualUserId]="actualUserId" appLucideIcons></app-sso>
            </ng-container>
        </mat-menu>
    </div>
</mat-toolbar>
