import { LucideAngularModule, icons } from 'lucide-angular';
import {Component, ElementRef, HostListener, Input, OnDestroy, OnInit} from '@angular/core';
import {NavigationStart, Router, RouterEvent} from '@angular/router';
import {AuthService} from '../../services/auth/auth.service';
import {SidebarsContainerService} from '../../services/sidebars-container.service';
import {SocketsService} from '../../services/sockets/sockets.service';
import {IsMobileService} from '../../services/is-mobile.service';
import {IssueService} from '../../services/issue.service';
import {environment} from '../../../environments/environment';
import {get} from 'underscore';
import { TourService, TourAnchorMatMenuDirective } from 'ngx-ui-tour-md-menu';
import {CaseManagementItemService} from '../../services/case-management-item-service';
import {UserService} from '../../services/user.service';
import {Subscription} from 'rxjs';
import {ChatItemService} from '../../services/chat-item-service';
import { MatToolbar } from '@angular/material/toolbar';
import {NgClass, NgIf, AsyncPipe, NgStyle} from '@angular/common';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { ReloginComponent } from '../relogin/relogin.component';
import { MatTooltip } from '@angular/material/tooltip';
import { NotificationMenuComponent } from '../notification/notification-menu/notification-menu.component';
import { UserAvatarDisplayComponent } from '../../shared/user-avatar-display/user-avatar-display.component';
import { MatMenuTrigger, MatMenu } from '@angular/material/menu';
import { SsoComponent } from '../sso/sso.component';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.scss'],
    standalone: true,
    imports: [MatToolbar, NgClass, DefaultClassDirective, NgIf, MatIconButton, MatIcon, ReloginComponent, MatTooltip, TourAnchorMatMenuDirective, NotificationMenuComponent, UserAvatarDisplayComponent, MatMenuTrigger, MatMenu, SsoComponent, AsyncPipe, TranslatePipe, LucideAngularModule]
})
export class HeaderComponent implements OnInit, OnDestroy {
    actualUserId = null;
    listType: string;
    applicationStage = '';
    logoSrc: string = '/assets/images/logo.svg';
    isAdmin: number;

    @Input() withoutUsersSidebar = true;

    userSubscription: Subscription;

    constructor(
        private authService: AuthService,
        private sidebarsContainer: SidebarsContainerService,
        public socketsService: SocketsService,
        public isMobileService: IsMobileService,
        private router: Router,
        private tourService: TourService,
        public issueService: IssueService,
        private _caseManagementItemService: CaseManagementItemService,
        private _chatItemService: ChatItemService,
        private elementRef: ElementRef,
        private _userService: UserService
    ) {
    }

    ngOnInit() {
        this.actualUserId = this.authService.getUserId();

        this.router.events.subscribe((routerEvent) => {
            if (routerEvent instanceof NavigationStart) {
                const keys = ['unaccepted', 'unassigned', 'my', 'delegated', 'sent', 'archives'];

                this.listType = keys.find(val => routerEvent.url.includes('/' + val));
            }
        });

        if (environment.apiUrl.includes('.beta.')) {
            this.applicationStage = 'Beta';
        } else if (environment.apiUrl.includes('.rc.')) {
            this.applicationStage = 'RC';
        }

        if (environment.appTitle === 'Porady') {
            this.logoSrc = '/assets/client/logo-client-light.svg';
        }

        this.checkIsAdmin();
    }

    ngOnDestroy() {
        this.userSubscription?.unsubscribe();
    }

    menuSidebarToggle() {
        this.sidebarsContainer.sidebar('menu').toggle();
    }

    usersSidebarToggle() {
        this.sidebarsContainer.sidebar('users').toggle();
    }

    onClick(event: MouseEvent) {
        this.issueService.closeDelegateMode();
    }

    public onInternalMode(): boolean {
        return <boolean>get(environment, 'internalMode', false);
    }

    public startTour() {
        this._caseManagementItemService.expandPanel();
        this._chatItemService.expandPanel();

        this.tourService.start();
    }

    checkIsAdmin() {
        this.userSubscription = this._userService.getUser(this.actualUserId).subscribe(data => {
            this.isAdmin = +data.is_admin;
        });
    }
}
