import {Component} from '@angular/core';
import {environment} from '../../../environments/environment';
import { ButtonComponent } from '../../elements/button/button.component';
import { MatTooltip } from '@angular/material/tooltip';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-relogin',
    template: `
    <a href="${environment.accountsUrl}/respondo/switch/client">
      <app-5ways-button iconLeft="arrow-right-left" variant="secondary" [disabled]="isDisabled" (click)="processing()" matTooltip="{{ 'LOGOUT.CLIENT-TOOLTIP' | translate }}">{{ 'LOGOUT.CLIENT' | translate }}</app-5ways-button>

    </a>
  `,
    imports: [ButtonComponent, MatTooltip, TranslatePipe]
})
export class ReloginComponent {
  isDisabled: boolean = false;

  constructor() { }

  processing() {
    this.isDisabled = true;
  }

}
