import { Component, OnInit } from '@angular/core';
import {PermissionService} from '../../services/permission.service';
import {Router} from '@angular/router';
import {ApplicationStateService} from '../../services/application-state.service';
import {forkJoin, timer} from 'rxjs';
import {switchMap} from 'rxjs/operators';
import {CustomerService} from '../../services/Customer.service';
import { DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';

@Component({
    selector: 'app-load-permission',
    templateUrl: './load-permission.component.html',
    styleUrls: ['./load-permission.component.scss'],
    standalone: true,
    imports: [DefaultLayoutAlignDirective]
})
export class LoadPermissionComponent implements OnInit {

    constructor(
        private permissionService: PermissionService,
        private router: Router,
        private CustomerService: CustomerService,
        private applicationStateService: ApplicationStateService
    ) {
    }

    ngOnInit() {
        timer(500)
            .pipe(switchMap(() =>
                forkJoin({
                    permissions: this.permissionService.getPermissions(),
                    customer: this.CustomerService.getList()
                })
            ))
            .subscribe(
            ({permissions, customer}) => {
                if (customer[0]) {
                    this.applicationStateService.setValue('customer', customer[0]);
                }

                if (permissions) {
                    this.permissionService.setPermissionNames((permissions as { results: [] }).results);

                    const urlBeforeLogout = localStorage.getItem('urlBeforeLogout');

                    this.applicationStateService.setValue('urlBeforeLogout', '');

                    localStorage.removeItem('urlBeforeLogout');

                    if (urlBeforeLogout) {
                        return this.router.navigateByUrl(urlBeforeLogout);
                    }

                    if (this.router.url === '/load-permission') {
                        this.router.navigateByUrl('start');
                    }
                }
            }
        );
    }

}
