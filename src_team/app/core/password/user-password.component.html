<form (ngSubmit)="submit()" fxLayout="column" fxLayoutAlign="center center">
    <h3>{{'PASSWORD.NEW-PASSWORD' | translate}}</h3>

    <input type="hidden" [(ngModel)]="user.id" name="user_id" value="{{user.id}}">
    <input type="hidden" [(ngModel)]="user.key" name="key" value="{{user.key}}">

    <mat-form-field appearance="outline">
        <mat-label>{{'PASSWORD.PASSWORD' | translate}}</mat-label>
        <input matInput type="password" [(ngModel)]="user.password" name="password">
    </mat-form-field>

    <mat-form-field appearance="outline">
        <mat-label>{{'PASSWORD.PASSWORD-REPEAT' | translate}}</mat-label>
        <input matInput type="password" [(ngModel)]="user.password_confirmation" name="password_confirmation">
    </mat-form-field>

    <button mat-raised-button color="primary" class="submit-button">{{'PASSWORD.SAVE' | translate}}</button>
</form>
