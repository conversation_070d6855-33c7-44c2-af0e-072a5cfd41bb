import {Component, inject, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {UserService} from '../../services/user.service';
import {AuthService} from '../../services/auth/auth.service';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {FormsModule} from '@angular/forms';
import {DefaultLayoutAlignDirective, DefaultLayoutDirective} from 'ngx-flexible-layout/flex';
import {MatFormField, MatLabel} from '@angular/material/select';
import {MatInput} from '@angular/material/input';
import {MatButton} from '@angular/material/button';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';
import {AlertService} from '../../services/alert.service';

@Component({
    selector: 'app-user-password',
    templateUrl: './user-password.component.html',
    styleUrls: ['./user-password.component.scss'],
    imports: [FormsModule, DefaultLayoutDirective, DefaultLayoutAlignDirective, MatFormField, MatLabel, MatInput, MatButton, TranslatePipe]
})
export class UserPasswordComponent implements OnInit {

    user = {
        password: '',
        password_confirmation: '',
        id: '',
        key: '',
        username: ''
    };

    errors; // FIXME: blokowalo się z flagą --prod

    private alertService: AlertService = inject(AlertService);

    constructor(
        private authService: AuthService,
        private route: ActivatedRoute,
        public userService: UserService,
        public translate: TranslateService,
        private router: Router) {
        if (authService.getAccessToken()) {
            this.router.navigate(['']);
        }
    }

    ngOnInit() {
        this.user.id = this.route.snapshot.params.id;
        this.user.key = this.route.snapshot.params.key;
        this.user.username = this.route.snapshot.params.username;
    }

    submit() {
        this.userService.resetPassword(this.user).subscribe(() => {
                const userlogin = this.authService.login({'username': atob(decodeURI(this.user.username)), 'password': this.user.password});

                userlogin.then(response => {
                    if (response === true) {
                        this.router.navigate(['issue/list/my']);
                    }
                }).catch(err => {
                    this.alertService.showAlert(this.translate.instant('PASSWORD.ERROR-LOGIN'), AlertType.ERROR, AlertDuration.MEDIUM);
                    this.router.navigate(['/']);
                });
            },
            error => {
                const errorMessages = error.error.errorMessages,
                    errorMessage = typeof errorMessages === 'string'
                        ? errorMessages
                        : Object.keys(errorMessages).reduce((res, v) => res.concat(errorMessages[v], ' '), '');

                this.alertService.showAlert(errorMessage, AlertType.ERROR, AlertDuration.MEDIUM);
            }
        );
    }

}
