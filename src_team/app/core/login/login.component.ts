import {Component, inject, OnInit, Renderer2} from '@angular/core';
import {ActivatedRoute, Router, RouterLink} from '@angular/router';
import {AuthService} from '../../services/auth/auth.service';
import {PermissionService} from '../../services/permission.service';
import {ApplicationStateService} from '../../services/application-state.service';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {FormsModule} from '@angular/forms';
import {DefaultLayoutAlignDirective, DefaultLayoutDirective} from 'ngx-flexible-layout/flex';
import {MatError, MatFormField, MatLabel} from '@angular/material/select';
import {MatInput} from '@angular/material/input';
import {EmailValidatorDirective} from '../../shared/validators/email-validator.directive';
import {NgIf} from '@angular/common';
import {MatButton} from '@angular/material/button';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';

@Component({
    selector: 'app-login',
    templateUrl: 'login.component.html',
    styleUrls: ['login.component.scss'],
    imports: [FormsModule, DefaultLayoutDirective, DefaultLayoutAlignDirective, MatFormField, MatLabel, MatInput, EmailValidatorDirective, NgIf, MatError, MatButton, RouterLink, TranslatePipe]
})
export class LoginComponent implements OnInit {
    user = {
        username: '',
        password: ''
    };

    private alertService: AlertService = inject(AlertService);

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private authService: AuthService,
        private renderer2: Renderer2,
        private permissionService: PermissionService,
        private applicationStateService: ApplicationStateService,
        public translate: TranslateService
    ) {
        if (authService.getAccessToken()) {
            const urlBeforeLogout = this.applicationStateService.getValue('urlBeforeLogout');

            this.applicationStateService.setValue('urlBeforeLogout', '');

            urlBeforeLogout
                ? this.router.navigateByUrl(urlBeforeLogout)
                : this.router.navigate(['start']);
        }
    }

    ngOnInit() {
        this.renderer2.selectRootElement('input[name="username"]').focus();

    }

    login(usercreds) {
        this.authService.login(usercreds).catch(err => {
            let msg;

            try {
                msg = err.error.toLowerCase().includes(this.translate.instant('LOGIN.ERROR.LIMIT'))
                    ? this.translate.instant('LOGIN.ERROR.LIMIT')
                    : this.translate.instant('LOGIN.ERROR.CHECK-DATA');
            } catch (e) {
                msg = this.translate.instant('LOGIN.ERROR.TRY-AGAIN');
            }

            this.alertService.showAlert(msg, AlertType.ERROR, AlertDuration.MEDIUM);
        });
    }
}
