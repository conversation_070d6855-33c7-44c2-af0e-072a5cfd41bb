<form (ngSubmit)="login(user)" fxLayout="column" fxLayoutAlign="center center">
    <h3>{{'LOGIN.LOGIN' | translate }}</h3>
    <mat-form-field appearance="outline">
        <mat-label>E-mail</mat-label>
        <input #username="ngModel" name="username" matInput [(ngModel)]="user.username" email type="email">
        <mat-error *ngIf="username.invalid && (username.dirty || username.touched)">{{'LOGIN.USERNAME.INVALID' | translate }}</mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline">
        <mat-label>{{'LOGIN.PASSWORD.PLACEHOLDER' | translate}}</mat-label>
        <input #password="ngModel" name="password" matInput type="password" [(ngModel)]="user.password" [minlength]="8">
        <mat-error *ngIf="password.invalid && (password.dirty || password.touched)">
            {{'LOGIN.PASSWORD.MIN-LENGTH' | translate }} {{password.errors.minlength.requiredLength}} {{'LOGIN.PASSWORD.SIGNS' | translate }}
        </mat-error>
    </mat-form-field>
    <button type="submit" mat-raised-button color="primary" [disabled]="username.invalid || password.invalid" class="submit-button">{{'LOGIN.SIGN-IN' | translate}}</button>
    <a href="#" routerLink="/restore-password" class="restore-password">{{'LOGIN.RESTORE-PASSWORD' | translate }}</a>
</form>
