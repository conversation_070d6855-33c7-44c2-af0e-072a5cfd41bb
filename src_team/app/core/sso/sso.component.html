<ng-container appLucideIcons>
    <button mat-menu-item (click)="openProfile()">
        <div class="profile">
            <app-user-avatar-display
                    class="settings-avatar"
                    [userId]="actualUserId"
                    [size]="26">
            </app-user-avatar-display>

            <span>{{'HEADER.PROFILE-SSO' | translate}}</span>
        </div>
    </button>
    <button mat-menu-item>
        <div routerLink="settings">
            <mat-icon>settings</mat-icon>
            {{'MENU-SIDEBAR.SETTINGS' | translate}}
        </div>
    </button>
    <button mat-menu-item (click)="logoutProfile()">
        <mat-icon>logout</mat-icon>
        {{'LOGOUT.LOGOUT' | translate}}
    </button>

</ng-container>
