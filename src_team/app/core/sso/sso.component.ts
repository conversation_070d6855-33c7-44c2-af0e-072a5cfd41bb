import {Component, Input, OnInit} from '@angular/core';
import {environment} from '../../../environments/environment';
import { MatMenuItem } from '@angular/material/menu';
import { UserAvatarDisplayComponent } from '../../shared/user-avatar-display/user-avatar-display.component';
import { RouterLink } from '@angular/router';
import { MatIcon } from '@angular/material/icon';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-sso',
    templateUrl: './sso.component.html',
    styleUrls: ['./sso.component.scss'],
    imports: [MatMenuItem, UserAvatarDisplayComponent, RouterLink, MatIcon, TranslatePipe]
})
export class SsoComponent implements OnInit {
    @Input()
      actualUserId: number;

    constructor() { }

    ngOnInit() {
    }

    openProfile() {
        window.location.href = environment.accountsUrl + '/user/profile';
    }

    logoutProfile() {
        window.location.href = environment.accountsUrl + '/externalLogout';
    }

}
