import {Component, OnInit} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import {filter, map, pluck, tap} from 'rxjs/operators';
import {LoadingStatus} from '../../../common/enums/loading-status.enum';
import {IsMobileService} from '../../../services/is-mobile.service';
import {IssueComplaintService} from '../../../services/issue-complaint.service';
import {IssueComplaintInterface} from '../../../common/interfaces/issue-complaint.interface';
import {ComplaintDetailsComponent} from '../complaint-details/complaint-details.component';
import { MatToolbar } from '@angular/material/toolbar';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { DefaultShowHideDirective, DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { NgIf, <PERSON><PERSON><PERSON>, NgClass, AsyncPipe } from '@angular/common';
import { MatCard } from '@angular/material/card';
import { RouterLink } from '@angular/router';
import { MatButton } from '@angular/material/button';
import { ListPaginatorComponent } from '../../../shared/list-paginator/list-paginator.component';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-complaint-list',
    templateUrl: './complaint-list.component.html',
    styleUrls: ['./complaint-list.component.scss'],
    imports: [MatToolbar, DefaultLayoutDirective, DefaultShowHideDirective, NgIf, NgFor, MatCard, DefaultLayoutAlignDirective, NgClass, DefaultClassDirective, RouterLink, MatButton, ListPaginatorComponent, MatProgressSpinner, AsyncPipe, TranslatePipe]
})
export class ComplaintListComponent implements OnInit {

    constructor(
        public isMobileService: IsMobileService,
        private issueComplaintService: IssueComplaintService,
        private dialog: MatDialog
    ) {
    }

    complaintsList: IssueComplaintInterface[] = [];
    complaintsTotal = 0;
    loadingStatus: LoadingStatus = LoadingStatus.loading;
    loadingStatusValues = LoadingStatus;
    limit = 10;
    page = 1;

    private fetchComplaints() {
        const conditions = '?limit=' + this.limit + '&page=' + this.page + '&order=id|desc';

        this.issueComplaintService.getComplaints(conditions)
            .pipe(
                tap(result => this.complaintsTotal = result.total),
                pluck('results'),
                map(results => results.map(item => item.IssueComplaint))
            )
            .subscribe(
                result => {
                    this.complaintsList = result.map(item => ({
                        ...item,
                        statusName: this.issueComplaintService.statusNames[item.status]
                    }));

                    this.loadingStatus = LoadingStatus.loaded;
                },
                err => {
                    console.error(err);
                    this.loadingStatus = LoadingStatus.error;
                }
            );
    }

    ngOnInit() {
        this.fetchComplaints();
    }

    onPageEvent(event: PageEvent) {
        this.limit = event.pageSize;
        this.page = ++event.pageIndex;
        this.fetchComplaints();
    }

    openComplaintDetails(complaint: IssueComplaintInterface) {
        this.dialog.open(ComplaintDetailsComponent, {
            width: '600px',
            data: complaint,
            panelClass: 'full-width-dialog'
        }).afterClosed()
            .pipe(filter(result => !!result))
            .subscribe(() => this.fetchComplaints());
    }
}
