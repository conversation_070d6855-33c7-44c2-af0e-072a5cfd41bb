<div class="complaint-list" [class.xs]="isMobileService.isXS | async">
    <mat-toolbar fxLayout fxHide.lt-lg></mat-toolbar>
    <ng-container *ngIf="loadingStatus === loadingStatusValues.loaded; else waiting ">
        <ng-container *ngIf="complaintsList.length; else noData">
            <mat-card
                *ngFor="let complaint of complaintsList"
                fxLayout.gt-xs="row wrap"
                fxLayout.xs="column"
                fxLayoutAlign.gt-xs="space-between center"
                fxLayoutAlign.xs="center"
                class="complaint"
            >
                <div class="status" [ngClass]="complaint.status">{{complaint.statusName}}</div>
                <a class="issue-id" [routerLink]="'/issue/'+ complaint.issue_id">{{'REPORTS.NUMBER-ISSUE' | translate}} {{complaint.issue_internal_id}}</a>
                <div class="description">{{'REPORTS.DESCRIPTION' | translate}} {{complaint.description}}</div>
                <button mat-button color="primary" (click)="openComplaintDetails(complaint)">Szczegóły</button>
            </mat-card>
            <app-list-paginator class="paginator" [pageSize]="10" [pageIndex]="page-1" [length]="complaintsTotal" (changePage)="onPageEvent($event)"></app-list-paginator>
        </ng-container>
    </ng-container>
</div>

<ng-template #waiting>
    <div fxLayoutAlign="center center" style="height: 100%; width: 100%;">
        <mat-spinner></mat-spinner>
    </div>
</ng-template>

<ng-template #noData>
    <div class="panel-info" fxLayoutAlign="center center">
        {{'REPORTS.NO-COMPLAINTS' | translate}}
    </div>
</ng-template>
