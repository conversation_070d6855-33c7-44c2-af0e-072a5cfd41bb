@use "../../../../variables" as *;

.complaint-list {
    font-size: 14px;
    height: calc(100% - 56px);
    overflow-x: hidden;
    overflow-y: auto;

    .complaint {
        margin-bottom: 2px;
        padding: 5px 10px;

        & > div {
            padding: 5px;
        }

        .description {
            width: 300px;
        }

        .issue-id {
            color: inherit;
            cursor: pointer;
            text-decoration: none;
            width: 160px;

            &:hover {
                text-decoration: underline;
            }
        }

        .status {
            border: 2px solid $grey;
            border-radius: 24px;
            color: $grey;
            display: inline-block;
            font-size: 12px;
            text-align: center;
            white-space: nowrap;
            width: 110px;

            &.new {
                border-color: $green;
                color: $green;
            }

            &.accepted {
                border-color: $brand-success;
                color: $brand-success;
            }

            &.rejected {
                border-color: $brand-danger;
                color: $brand-danger;
            }
        }
    }

    &.xs .complaint {
        .issue-id, .description {
            margin-top: 10px;
            padding-left: 4px;
            text-align: left;
        }

        .status, button {
            text-align: center;
            width: 100%;
        }
    }

    .paginator {
        bottom: 0;
        left: 0;
        position: absolute;
        right: 0;
    }
}

:host::ng-deep .mat-paginator-outer-container {
    padding-right: 20px;
}
