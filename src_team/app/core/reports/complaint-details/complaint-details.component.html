<div class="complaint-details">
    <h3 mat-dialog-title>{{'REPORTS.NUMBER-COMPLAINT' | translate}} {{data.issue_internal_id}}</h3>
    <mat-dialog-content>
        <div><strong>{{'REPORTS.STATUS' | translate}}</strong> {{data.statusName}}</div>
        <div><strong>{{'REPORTS.COMPLAINT' | translate}}</strong> {{data.issue_complaint_reason_name}}</div>
        <div>
            <strong>{{'REPORTS.DESCRIPTION' | translate}}</strong>
            <textarea
                matInput
                name="description"
                [value]="data.description"
                [cdkTextareaAutosize]="true"
                [cdkAutosizeMaxRows]="14"
                readonly
            ></textarea>
        </div>
        <div><strong>{{'REPORTS.CREATION' | translate}}</strong> {{data.created}}</div>
        <div *ngIf="data.comment"><strong>{{'REPORTS.COMMENT' | translate}}</strong>
            <textarea
                matInput
                name="description"
                [value]="data.comment"
                [cdkTextareaAutosize]="true"
                [cdkAutosizeMaxRows]="14"
                readonly
            ></textarea>
        </div>
    </mat-dialog-content>
    <mat-dialog-actions fxLayout fxLayoutAlign="space-between">
        <button mat-raised-button matDialogClose>{{'REPORTS.CANCEL' | translate}}</button>
        <div *ngIf="data.status === 'new' && permissionService.checkPermission('issueComplaintUpdate')" fxLayout
             fxLayoutAlign="space-between">
            <button mat-raised-button color="warn" (click)="rejectComplaint()">{{'REPORTS.REJECT' | translate}}</button>
            <button mat-raised-button color="primary" (click)="acceptComplaint()">{{'REPORTS.ACCEPT' | translate}}</button>
        </div>
    </mat-dialog-actions>
</div>

