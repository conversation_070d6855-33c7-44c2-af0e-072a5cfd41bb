import {Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import { NavigationEnd, Router, RouterEvent, RouterLink, RouterOutlet } from '@angular/router';
import {filter} from 'rxjs/operators';
import {Subscription} from 'rxjs';
import { MatToolbar } from '@angular/material/toolbar';
import { DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { DefaultShowHideDirective, DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { MatIconButton } from '@angular/material/button';
import { MatMenuTrigger, MatMenu, MatMenuItem } from '@angular/material/menu';
import { MatIcon } from '@angular/material/icon';
import { NgIf, NgClass } from '@angular/common';
import { CheckPermissionNamePipe } from '../../shared/pipes/check-permission-name.pipe';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-reports',
    templateUrl: './reports.component.html',
    styleUrls: ['./reports.component.scss'],
    imports: [MatToolbar, DefaultLayoutAlignDirective, DefaultShowHideDirective, MatIconButton, MatMenuTrigger, MatIcon, MatMenu, NgIf, MatMenuItem, RouterLink, NgClass, DefaultClassDirective, RouterOutlet, CheckPermissionNamePipe, TranslatePipe]
})
export class ReportsComponent implements OnInit, OnDestroy {
    currentUrl = '';
    subscription: Subscription;

    constructor(public router: Router) {
    }

    ngOnInit() {
        this.currentUrl = this.router.routerState.snapshot.url;

        this.subscription = this.router.events
            .pipe(
                filter((e): e is NavigationEnd => e instanceof NavigationEnd)
            )
            .subscribe(event => {
                this.currentUrl = event.url;
            });
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription?.unsubscribe();
        }
    }
}
