<mat-toolbar fxLayoutAlign="end center" fxHide.gt-md>
    <button mat-icon-button [matMenuTriggerFor]="settingsSubmenu">
        <mat-icon>menu</mat-icon>
    </button>
    <mat-menu #settingsSubmenu="matMenu">
        <button mat-menu-item routerLink="/reports/users" *ngIf="'reports' | checkPermissionName">{{'REPORTS.WORK-REPORT' | translate}}</button>
    </mat-menu>
</mat-toolbar>
<div
    class="reports-panel"
    [ngClass]="{ 'users': currentUrl.includes('users') }">
    <router-outlet></router-outlet>
</div>
