<ng-container *ngIf="loaded">
    <div mat-dialog-content>
        <table mat-table [dataSource]="dataSource">
            <ng-container matColumnDef="month">
                <th mat-header-cell *matHeaderCellDef> {{'REPORTS.MONTH' | translate}}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.month | getMonthName}}
                </td>
                <td mat-footer-cell *matFooterCellDef><b>{{'REPORTS.SUM' | translate}}</b></td>
            </ng-container>

            <ng-container matColumnDef="issuesCount">
                <th mat-header-cell *matHeaderCellDef> {{'REPORTS.ISSUE-NUMBER' | translate}}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.issues}}
                </td>
                <td mat-footer-cell *matFooterCellDef><b>{{getTotal('issues')}}</b></td>
            </ng-container>

            <ng-container matColumnDef="sentencesCount">
                <th mat-header-cell *matHeaderCellDef> {{'REPORTS.NUMBER-POSTS' | translate}}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.sentences}}
                </td>
                <td mat-footer-cell *matFooterCellDef><b>{{getTotal('sentences')}}</b></td>
            </ng-container>

            <ng-container matColumnDef="approved">
                <th mat-header-cell *matHeaderCellDef> {{'REPORTS.NUMBER-APPROVED' | translate}}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.approved}}
                </td>
                <td mat-footer-cell *matFooterCellDef><b>{{getTotal('approved')}}</b></td>
            </ng-container>

            <ng-container matColumnDef="rejected">
                <th mat-header-cell *matHeaderCellDef> {{'REPORTS.NUMBER-REJECT' | translate}}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.rejected}}
                </td>
                <td mat-footer-cell *matFooterCellDef><b>{{getTotal('rejected')}}</b></td>
            </ng-container>

            <ng-container matColumnDef="corrected">
                <th mat-header-cell *matHeaderCellDef> {{'REPORTS.NUMBER-CORRECTIONS' | translate}}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.corrected}}
                </td>
                <td mat-footer-cell *matFooterCellDef><b>{{getTotal('corrected')}}</b></td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            <tr mat-footer-row *matFooterRowDef="displayedColumns;"></tr>
        </table>
    </div>
    <div mat-dialog-actions>
        <mat-form-field>
            <mat-select placeholder="{{'REPORTS.YEAR' | translate}}" (selectionChange)=onSelectValue($event.value) [(value)]="currentYear">
                <mat-option *ngFor="let year of years" [value]="year">
                    {{year}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <div fxFlex></div>
        <button mat-button mat-dialog-close>{{'REPORTS.CLOSE' | translate}}</button>
    </div>
</ng-container>

<div class="loading" *ngIf="!loaded" fxLayoutAlign="center center">
    <mat-spinner></mat-spinner>
</div>
