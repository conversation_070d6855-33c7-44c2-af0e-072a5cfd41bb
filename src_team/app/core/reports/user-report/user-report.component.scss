@use '../../../../variables' as *;
@use '@angular/material' as mat;

.user-report-card {
    padding: 0 20px;
    flex-direction: column;
    overflow: auto;

    /* Style dla paginatora */
    app-5ways-paginator {
        margin: 10px 0;
    }

    /* Style dla przycisków akcji */
    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        margin: 16px 10px 0;

        button {
            margin: 0 8px 8px;
            height: 36px;
        }

        .download-btn-with-text {
            background-color: $fiveways-button-bg;
            color: $fiveways-button-text;
        }
    }

    .filters {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        margin: 0 10px;

        /* Grupowanie filtrów */
        app-user-group-filter,
        app-user-role-filter,
        app-user-status-filter,
        mat-form-field {
            margin: 0 8px 8px 8px;
        }

        /* Przyciski akcji */
        .d-flex {
            display: flex;
            align-items: center;
            margin: 8px 0 0 8px;
        }

        &-btn {
            margin: 0 8px;
            height: 36px;

            &-download {
                max-height: 43px;
                min-width: 160px;
            }
        }

        .search {
            position: relative;
            margin-right: 8px;
            min-width: 300px;


            .search-input {
                width: 350px;
            }

            .toolbar-icon {
                cursor: pointer;
                color: $fiveways-button-text;
                font-size: 24px;
                display: inline-block;

                &-search {
                    margin-left: 5px;
                    position: relative;
                    left: 10px;
                }
            }
        }

        app-user-group-filter,
        app-user-role-filter,
        app-user-status-filter {
            margin: 0 8px;
        }

        mat-form-field {
            margin: 0 8px;
        }

        .d-flex {
            display: flex;
            align-items: center;
            margin-left: 8px;
        }

        &.is-mobile {
            align-items: flex-end;
            flex-direction: column;
            margin-top: 10px;
            width: 100%;

            .mat-form-field,
            app-user-group-filter,
            app-user-role-filter,
            app-user-status-filter {
                width: 100%;
                margin: 8px 0;
            }
        }
    }

    /* Style dla widoku mobilnego */
    &.is-mobile {
        .action-buttons {
            flex-direction: column;
            align-items: stretch;
            width: 100%;

            button {
                width: 100%;
                margin: 4px 0;
            }
        }
    }

    .user-report {
        margin-top: 15px;
        overflow: hidden;
        width: 100%;
        padding-bottom: 26px;
        border-radius: 14px;
    }

    .download-btn {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 40px;
        max-height: 52px;

        @media (min-width: 708px) {
            max-height: 44px
        }
    }

    .download-btn-with-text {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 160px;
        max-height: 52px;
        margin: 0 8px;
        background-color: $fiveways-button-bg;
        color: $fiveways-button-text;

        @media (min-width: 708px) {
            max-height: 44px
        }

        mat-icon {
            margin-right: 8px;
            width: 20px;
            height: 20px;
            font-size: 20px;
        }
    }
}

.ag-grid-container {
    margin-top: 15px;
    overflow: hidden;
    width: 100%;
    padding-bottom: 0;

    .ag-theme-alpine {
        overflow: hidden;
        margin-bottom: 0;

        .user-name {
            cursor: pointer;
            color: $dark-blue;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .summary-row {
        display: flex;
        background-color: $fiveways-light-gray-background;
        border-bottom: 1px solid $fiveways-stroke-2;
        border-left: 1px solid $fiveways-stroke-2;
        border-right: 1px solid $fiveways-stroke-2;
        border-radius: 0 0 $container-border-radius $container-border-radius;
        padding: 12px 0;
        margin-top: -1px;

        .summary-cell {
            flex: 1;
            padding: 0 16px;
            font-size: 14px;
            display: flex;
            align-items: center;

            &.name-cell {
                flex: 2;
            }
        }
    }
}


.loading {
    height: 55vh;
    overflow: hidden;
}


.mat-form-field-outline-start {
    border-radius: 7px 0 0 7px;
    min-width: 7px;
}

.mat-form-field-outline-end {
    border-radius: 0 7px 7px 0;
    min-width: 7px;
}

.mat-form-field-appearance-outline mat-label {
    padding-right: 23px;
}

.mat-select-value, .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    position: relative;
    bottom: 3px;
}

.no-data {
    font-size: 30px;
    font-weight: bold;
    letter-spacing: 2px;
    opacity: 0.2;
    display: flex;
    justify-content: center;
    height: 60vh;
    align-items: center;
}
