<div class="user-report-card" fxLayout="column" ngClass.xs="is-mobile">
    <mat-card-actions>
        <div class="filters" fxLayout="row wrap" fxLayout.xs="column" fxLayoutAlign="end end" fxLayoutAlign.xs="space-between end">
            <div class="filters" ngClass.xs="is-mobile" ngClass.md="is-tablet">
                <mat-form-field appearance="outline" class="search">
                    <input
                            autocomplete="off"
                            matInput
                            class="search-input"
                            [(ngModel)]="userNameFilter"
                            (keydown.enter)="getReportData()"
                            (ngModelChange)="onUserNameFilterChange()">
                    <mat-icon matSuffix class="toolbar-icon toolbar-icon-search">
                        search
                    </mat-icon>
                    <mat-icon *ngIf="userNameFilter" matSuffix class="toolbar-icon" (click)="clearUserNameFilter()">
                        close
                    </mat-icon>
                </mat-form-field>
                <app-user-group-filter (changeGroup)="onGroupChange($event)" [selectedValue]="groupFilterId" ngClass.xs="full-width"></app-user-group-filter>
                <app-user-role-filter (changeRole)="onRoleChange($event)" [selectedValue]="roleFilterVal" ngClass.xs="full-width"></app-user-role-filter>
                <app-user-status-filter (changeStatus)="onStatusChange($event)" [selectedValue]="statusFilterVal" ngClass.xs="full-width"></app-user-status-filter>
                <mat-form-field appearance="outline">
                    <mat-label>{{'REPORTS.FROM' | translate}}</mat-label>
                    <input
                            matInput
                            [matDatepicker]="startPicker"
                            [formControl]="dateStart"
                            readonly
                            (keydown)="preventDefault($event)"
                            (dateChange)="onDateChange($event.value, 'start')">
                    <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                    <mat-datepicker #startPicker></mat-datepicker>
                </mat-form-field>
                <mat-form-field appearance="outline">
                    <mat-label>{{'REPORTS.TO' | translate}}</mat-label>
                    <input
                            matInput
                            [matDatepicker]="endPicker"
                            [formControl]="dateEnd"
                            readonly
                            (keydown)="preventDefault($event)"
                            (dateChange)="onDateChange($event.value, 'end')">
                    <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                    <mat-datepicker #endPicker></mat-datepicker>
                </mat-form-field>
            </div>

            <div class="action-buttons">
                <button mat-button class="filters-btn mat-primary" type="submit" [ngStyle.xs]="{'margin-bottom': '10px'}" (click)="getReportData()">
                    <mat-icon>
                        filter_list
                    </mat-icon>
                    {{'REPORTS.FILTER' | translate}}
                </button>
                <button mat-button class="filters-btn mat-primary" type="button" [ngStyle.xs]="{'margin-bottom': '10px'}" (click)="resetFilters()">
                    <mat-icon>
                        refresh
                    </mat-icon>
                    {{'REPORTS.RESET' | translate}}
                </button>
                <button mat-button class="download-btn-with-text mat-primary" type="button" [ngStyle.xs]="{'margin-bottom': '10px'}" (click)="exportXls()">
                    <mat-icon svgIcon="download-icon"></mat-icon>
                    {{'REPORTS.DOWNLOAD-RAPORT' | translate}}
                </button>
            </div>

        </div>
    </mat-card-actions>

    <app-5ways-paginator
            [pageSize]="pageSize"
            [pageIndex]="pageIndex"
            [length]="total || 0"
            (page)="onPageChange($event, $event.pageIndex + 1)">
    </app-5ways-paginator>

    @if (loaded) {
        @if (total <= 0) {
            <div class="no-data">
                {{ 'REPORTS.NO-DATA' | translate}}
            </div>
        } @else {
            <div class="table-wrapper">
                <div class="ag-grid-container">
                    <ag-grid-angular
                            class="ag-theme-alpine custom-padding"
                            [rowData]="rowData"
                            [columnDefs]="columnDefs"
                            [defaultColDef]="defaultColDef"
                            [gridOptions]="gridOptions"
                            [getRowStyle]="getRowStyle"
                            (gridReady)="onGridReady($event)"
                            (sortChanged)="applySort($event)">
                    </ag-grid-angular>

                    <div class="summary-row" *ngIf="rowData && rowData.length > 0">
                        <div class="summary-cell name-cell"><b>{{'REPORTS.SUM' | translate}}</b></div>
                        <div class="summary-cell"><b>{{getTotal('issues')}}</b></div>
                        <div class="summary-cell"><b>{{getTotal('sentences')}}</b></div>
                        <div class="summary-cell"><b>{{getTotal('approved')}}</b></div>
                        <div class="summary-cell"><b>{{getTotal('rejected')}}</b></div>
                        <div class="summary-cell"><b>{{getTotal('corrected')}}</b></div>
                    </div>
                </div>
            </div>
        }
    } @else {
        <div class="loading" fxLayoutAlign="center center">
            <mat-spinner></mat-spinner>
        </div>
    }
</div>
