import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {AuthService} from '../../../services/auth/auth.service';
import {PermissionService} from '../../../services/permission.service';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-default-route',
    templateUrl: './default-reports-route.component.html',
    imports: [TranslatePipe]
})
export class DefaultReportsRouteComponent implements OnInit {

    constructor(private router: Router, authService: AuthService, private permissionService: PermissionService) {
    }

    ngOnInit() {
        if (this.permissionService.checkPermission('reports')) {
            this.router.navigateByUrl('reports/users');
        }
    }
}
