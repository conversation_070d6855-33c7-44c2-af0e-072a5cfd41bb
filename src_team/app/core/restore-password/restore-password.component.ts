import {Component} from '@angular/core';
import {RestorePasswordService} from '../../services/restore-password.service';
import { HttpErrorResponse } from '@angular/common/http';
import { UntypedFormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';
import { MatFormField, MatLabel, MatError } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { EmailValidatorDirective } from '../../shared/validators/email-validator.directive';
import { MatButton } from '@angular/material/button';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-restore-password',
    templateUrl: './restore-password.component.html',
    styleUrls: ['./restore-password.component.scss'],
    imports: [DefaultLayoutDirective, DefaultLayoutAlignDirective, NgIf, RouterLink, MatFormField, MatLabel, MatInput, FormsModule, EmailValidatorDirective, ReactiveFormsModule, MatError, MatButton, TranslatePipe]
})
export class RestorePasswordComponent {
    resetPasswordSent = false;
    passwordFormCtrl = new UntypedFormControl('', [Validators.required, Validators.email]);

    constructor(private restorePasswordService: RestorePasswordService) {
    }

    restorePassword() {
        this.restorePasswordService.restorePassword(this.passwordFormCtrl.value).subscribe(
            () => this.resetPasswordSent = true,
            (err: HttpErrorResponse) => {
                console.error(err);
                err.status === 404
                    ? this.passwordFormCtrl.setErrors({email404: true})
                    : this.passwordFormCtrl.setErrors({unknown: true});
            }
        );
    }
}
