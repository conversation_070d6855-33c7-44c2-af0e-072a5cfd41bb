<div class="chat-thread">
    <div class="backdrop"></div>
    <mat-drawer-container>
        <mat-drawer-content>
            <ng-container>
                <div class="thread-header">
                    <a mat-button fxHide.gt-xs>
                        <mat-icon>arrow_back</mat-icon>
                    </a>
                    <div class="thread-info">
                        <span fxLayout fxLayoutAlign="space-between">
                            <span class="client-name"><PERSON></span>
                        </span>
                    </div>
                </div>
            </ng-container>
            <hr class="ruler">
            <div class="messages-list">
                <ng-container *ngFor="let message of messages;">
                    <ng-container>
                        <ng-container>
                            <div class="chat-message {{message.type}}">
                                <div class="chat-message__sender">
                                    <ngx-avatars [size]="40" [name]="message.author" [bgColor]="message.avatarColor"></ngx-avatars>
                                </div>
                                <div>
                                    <div class="chat-message__text">
                                        {{message.text}}
                                    </div>
                                    <div class="chat-message chat-message--date {{message.type}}">
                                        <mat-icon class="chat-message__read-icon">check_circle</mat-icon>
                                        <span class="chat-message__datetime">{{message.createdAt}}</span>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </ng-container>
                </ng-container>
            </div>
            <div class="chat-input">
                <textarea class="textarea">Ambusher maze wocka wocka fruit Pac-Man Fever arcade Galaxian Boss power up intermission.</textarea>
                <div class="toolbar" matTooltip="{{'LIVECHAT-WFIRMA.ATTACHMENT-ADD' | translate}}">
                    <button class="button-icon">
                        <mat-icon>add_link</mat-icon>
                    </button>
                    <button mat-stroked-button class="button-rounded primary" color="primary">
                        {{'LIVECHAT-WFIRMA.SEND' | translate}}
                    </button>
                </div>
            </div>
        </mat-drawer-content>
    </mat-drawer-container>
</div>
