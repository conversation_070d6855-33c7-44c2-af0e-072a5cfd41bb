@use '../../../../../variables' as *;

$thread-info-height: 55px;

.chat-thread {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    width: 100%;
    z-index: 999;

    mat-drawer-container {
        border-radius: $container-border-radius;
    }

    .backdrop {
        -webkit-backdrop-filter: blur(2px);
        backdrop-filter: blur(2px);
        background: rgba(246, 248, 250, 0.3);
        height: 100%;
        position: absolute;
        width: 100%;
        z-index: 100;
    }

    .client-data {
        width: 320px;
    }

    .sidebar-toggle {
        position: absolute;
        right: 10px;
        top: 4px;
        z-index: 9999;
    }

    .mat-drawer-container {
        background-color: #fff;
        height: 100%;
    }

    .messages-container {
        padding: 10px;
        width: 100%;
        overflow: hidden;
        overflow-y: auto;
    }

    .messages-list {
        @extend .messages-container;
        height: calc(100% - #{$livechat-wfirma-input-height} - #{$thread-info-height} - 5px);
    }

    .messages-blocked-list {
        @extend .messages-container;
        height: calc(100% - #{$thread-info-height} - 5px);
    }

    .thread-header {
        align-items: center;
        display: flex;
        justify-content: space-between;
        min-height: $thread-info-height;
        padding: 0 10px;

        .thread-info {
            display: flex;
            flex-direction: column;
            margin-left: 10px;
            margin-right: 50px;
            width: 100%;

            .client-name {
                color: #1270B6;
                font-size: 16px;
                font-weight: 500;
            }
        }
    }

    .ruler {
        background-color: $grey;
        border-width: 0.5px;
        margin-bottom: 0;
        margin-top: 0;
        opacity: 0.6;
        width: calc(100% - 40px);
    }

    .thread-closed {
        position: relative;

        span {
            background-color: #fff;
            left: 50%;
            padding: 0 10px;
            position: absolute;
            top: 0;
            transform: translate(-50%, -50%);
        }
    }
}

.spinner-wrapper {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%;
}
