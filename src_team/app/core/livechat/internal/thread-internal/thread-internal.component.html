<ng-container *ngIf="!(isLoading$ | async); else loadingMessage">
    <div class="chat-thread" ngClass.xs="xs">
        <mat-drawer-container>
            <mat-drawer-content>
                <ng-container>
                    <div class="thread-header">
                        <a mat-button routerLink="/chat" fxHide.gt-xs>
                            <mat-icon>arrow_back</mat-icon>
                        </a>
                        <div class="thread-info" *ngIf="thread">
                            <span fxLayout fxLayoutAlign="space-between">
                                <span *ngIf="thread?.user?.firstname && thread?.user?.lastname" class="client-name">
                                  {{ thread.user.firstname + ' ' + thread.user.lastname }}
                                    <ng-container *ngIf="+thread?.user?.banned"><span>({{'LIVECHAT-WFIRMA.USER-BLOCKED' | translate}})</span></ng-container>
                                </span>
                            </span>
                        </div>
                    </div>
                </ng-container>
                <hr class="ruler">
                <div #messageList
                     class="messages-container"
                     [ngClass]="isUserBanned ? 'messages-blocked-list' : 'messages-list'"
                     (scroll)="onScroll($event)">
                    <ng-container *ngFor="let message of messages; let lastMessage = last">
                        <app-message-internal
                                [message]="message"
                        ></app-message-internal>
                        <app-message-internal-typing *ngIf="lastMessage" [threadOwnerId]="thread?.owner_id"></app-message-internal-typing>
                    </ng-container>
                </div>
                <ng-container *ngIf="!isUserBanned">
                    <app-chat-internal-input [threadId]="thread.id">
                    </app-chat-internal-input>
                </ng-container>
            </mat-drawer-content>
        </mat-drawer-container>
    </div>
</ng-container>

<ng-template #loadingMessage>
    <div class="spinner-wrapper">
        <mat-spinner></mat-spinner>
    </div>
</ng-template>
