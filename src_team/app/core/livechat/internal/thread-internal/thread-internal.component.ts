import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, ElementRef, OnDestroy, OnInit, ViewChild} from '@angular/core';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {ChatInternalThread} from '../../../../common/interfaces/chat-internal-thread.interface';
import {distinctUntilChanged, finalize, map, take} from 'rxjs/operators';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {BehaviorSubject, combineLatest, forkJoin, Subject, Subscription} from 'rxjs';
import {ThreadsInternalService} from '../../../../services/livechat-wfirma/threads-internal.service';
import {ChatInternalMessages} from '../../../../common/interfaces/chat-internal-messages.interface';
import {UserService} from '../../../../services/user.service';
import {MessagingService} from '../../../../services/messaging.service';
import {AuthService} from '../../../../services/auth/auth.service';
import {MessagesInternalService} from '../../../../services/livechat-wfirma/messages-internal.service';
import {ChatInternalUserThread} from '../../../../common/interfaces/chat-internal-user-thread.interface';
import {IsMobileService} from '../../../../services/is-mobile.service';
import {ApplicationSettingsService} from '../../../../services/application-settings.service';
import {SocketsService} from '../../../../services/sockets/sockets.service';
import {SocketChatInternalMessagesService} from '../../../../services/sockets/socket-chat-internal-messages.service';
import { NgIf, NgClass, NgFor, AsyncPipe } from '@angular/common';
import { DefaultClassDirective, DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { MatDrawerContainer, MatDrawerContent } from '@angular/material/sidenav';
import { MatAnchor } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { MessageInternalComponent } from '../message-internal/message-internal.component';
import { MessageInternalTypingComponent } from '../message-internal-typing/message-internal-typing.component';
import { ChatInternalInputComponent } from '../chat-internal-input/chat-internal-input.component';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-thread-internal',
    templateUrl: './thread-internal.component.html',
    styleUrls: ['./thread-internal.component.scss'],
    imports: [NgIf, DefaultClassDirective, MatDrawerContainer, MatDrawerContent, MatAnchor, RouterLink, DefaultShowHideDirective, MatIcon, DefaultLayoutDirective, DefaultLayoutAlignDirective, NgClass, NgFor, MessageInternalComponent, MessageInternalTypingComponent, ChatInternalInputComponent, MatProgressSpinner, AsyncPipe, TranslatePipe]
})
export class ThreadInternalComponent implements OnInit, OnDestroy, AfterViewChecked {
    @ViewChild('messageList') private messageList: ElementRef;

    thread: ChatInternalThread;
    messages: ChatInternalMessages[] = [];
    sidebarOpened = false;
    isLoading$ = new BehaviorSubject<boolean>(true);

    public wait = false;

    private loading: false | 'previous' | 'next' = 'next';
    private SCROLL_LOAD_POSITION = 50;
    private doScroll: false | 'top' | 'bottom' = false;
    private CHAT_MESSAGE_PACKET_SIZE: number;
    private lastScrollPosition = Number.MAX_SAFE_INTEGER;
    private firstMessageInDatabaseId: number;
    private subscription: Subscription;
    private subscriptions = new Subscription();
    private messagesFetched$ = new Subject<number>();
    private lastThreadId: number;

    constructor(
        public translate: TranslateService,
        private _route: ActivatedRoute,
        private _threadsInternalService: ThreadsInternalService,
        private _messagesInternalService: MessagesInternalService,
        private _userService: UserService,
        private _messagingService: MessagingService,
        private _isMobileService: IsMobileService,
        private _authService: AuthService,
        private _applicationSettingsService: ApplicationSettingsService,
        private _socketChatInternalMessagesService: SocketChatInternalMessagesService,
        private _socketsService: SocketsService,
        private changeDetectorRef: ChangeDetectorRef
    ) {
        this.CHAT_MESSAGE_PACKET_SIZE = this._applicationSettingsService.getValue('CHAT_MESSAGE_PACKET_SIZE');
    }

    ngOnInit() {
        const routeParams$ = this._route.params.pipe(map(params => +params.id), distinctUntilChanged());

        this.subscriptions.add(
            routeParams$.subscribe(threadId => {
                this.isLoading$.next(true);
                this.messages = [];
                this.thread = null;
                this.firstMessageInDatabaseId = null;
                this.loading = 'next';
                this.lastScrollPosition = Number.MAX_SAFE_INTEGER;
                this.wait = false;
                this.loadMessagesFirstTime(threadId);
            })
        );

        this.subscriptions.add(
            combineLatest([
                routeParams$.pipe(distinctUntilChanged()),
                this._socketsService.connected$,
                this.messagesFetched$.pipe(distinctUntilChanged())
            ]).subscribe(
                ([threadId, socketsConnected, messagesFetched]) => {
                    if (threadId && socketsConnected && messagesFetched) {
                        this.setSocketsSubscription();
                    }
                }
            )
        );
    }

    private _processAndAddMessage(message: ChatInternalMessages, otherUser): boolean {
        if (!this.findMessageById(+message.id)) {
            const loggedUserId = this._authService.getUserId();
            message.user = +message.user_id === loggedUserId ? null : otherUser;
            message.user_type = +message.user_id === loggedUserId ? 'loggedUser' : 'user';
            message['userType'] = message.user_type === 'loggedUser' ? 'loggedUser' : 'user';

            this.messages.push(message);
            return true;
        }
        return false;
    }

    private loadMessagesFirstTime(threadId: number) {
        forkJoin([
            this._messagesInternalService.getFirstMessageInThreadId(threadId),
            this._messagesInternalService.getMessages(threadId, null, 'desc', this.CHAT_MESSAGE_PACKET_SIZE),
            this._threadsInternalService.getThread(threadId),
            this._threadsInternalService.getUserThread(threadId)
        ]).pipe(
            finalize(() => this.isLoading$.next(false))
        ).subscribe(([firstMessageId, messages, thread, userThreads]) => {
                this.thread = thread[0];
                this.firstMessageInDatabaseId = firstMessageId;

                const otherUserThread = userThreads.find((ut: ChatInternalUserThread) => +ut.user_id !== this._authService.getUserId());

                if (otherUserThread) {
                    this._userService.getUser(otherUserThread.user_id).pipe(
                        take(1),
                        map(user => {
                            this.thread.user = user;
                            return user;
                        })
                    ).subscribe(user => {
                        this.messages = messages.map(message => ({
                            ...message,
                            user: +message.user_id === this._authService.getUserId() ? null : user,
                            user_type: +message.user_id === this._authService.getUserId() ? 'loggedUser' : 'user'
                        }));

                        this.sortMessages();
                        this.messagesFetched$.next(this.thread.id);
                        this.setScrollPosition();
                        this.setOpenSidebarStatus();
                        setTimeout(() => this.wait = true, 500);
                    });
                } else {
                    this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-USER-THREAD'));
                }
            },
            () => {
                this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-MESSAGES'));
                this.messages = [];
                this.thread = null;
            });
    }

    private loadPreviousMessages() {
        if (this.allMessagesFetched || !this.thread || !this.thread.user) {
            this.loading = false;

            return;
        }

        const otherUser = this.thread.user;
        const firstMessageIdCurrently = this.messages.length > 0 ? +this.messages[0].id : null;

        if (!firstMessageIdCurrently) {
            this.loading = false;
            return;
        }

        this._messagesInternalService.getMessages(+this.thread.id, firstMessageIdCurrently, 'desc', this.CHAT_MESSAGE_PACKET_SIZE).subscribe(
            olderMessages => {
                let added = false;
                olderMessages.forEach(msg => {
                    if (this._processAndAddMessage(msg, otherUser)) {
                        added = true;
                    }
                });

                if (added) {
                    this.sortMessages();
                }
                this.setScrollPosition();
                if (added) {
                    this.changeDetectorRef.markForCheck();
                }
            },
            () => {
                this.loading = false;
                this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-DOWNLOADING'));
            }
        );
    }

    private loadRemainingMessages() {
        if (!this.thread || !this.thread.user) {
            console.error('Thread or thread user not loaded before loading remaining messages.');
            return;
        }
        const otherUser = this.thread.user;
        const currentLastMessageId = this.lastMessageId;

        this._messagesInternalService.getMessages(+this.thread.id, currentLastMessageId, 'asc', 9999).pipe(
        ).subscribe(
            newMessages => {
                let added = false;
                newMessages.forEach(msg => {
                    if (this._processAndAddMessage(msg, otherUser)) {
                        added = true;
                    }
                });

                if (added) {
                    this.sortMessages();
                    this._threadsInternalService.triggerRefreshThreads(newMessages);

                    this.doScroll = 'bottom';
                    this.changeDetectorRef.markForCheck();
                }
            },
            () => {
                this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-MESSAGES'));
            }
        );
    }


    onScroll(event) {
        if (this.loading) {
            return;
        }

        const scrollbarPosition = +event.target.scrollTop;
        const scrollUP = this.checkScrollDirectionUp(scrollbarPosition);
        const reachedTop = this.reachedTop(scrollbarPosition);

        if (scrollUP && reachedTop) {
            this.loading = 'previous';
            this.loadPreviousMessages();
        }
    }

    private checkScrollDirectionUp(position: number): boolean {
        const isUp = position < this.lastScrollPosition;
        this.lastScrollPosition = position;
        return isUp;
    }

    private reachedTop(scrollbarPosition: number): boolean {
        return scrollbarPosition < this.SCROLL_LOAD_POSITION;
    }

    private setScrollPosition() {
        if (this.loading === 'previous') {
            if (!this.allMessagesFetched) {
                this.doScroll = 'top';
            }
            this.lastScrollPosition = this.SCROLL_LOAD_POSITION + 1;
        } else {
            this.doScroll = 'bottom';
            this.lastScrollPosition = Number.MAX_SAFE_INTEGER;
        }

        this.loading = false;
    }

    private scrollToEnableLoadMore() {
        if (this.messageList && this.messageList.nativeElement) {
            this.messageList.nativeElement.scrollTop = this.SCROLL_LOAD_POSITION + 50;
        }
    }

    private scrollToBottom() {
        try {
            if (this.messageList && this.messageList.nativeElement) {
                this.messageList.nativeElement.scrollTop = this.messageList.nativeElement.scrollHeight;
            }
        } catch (err) {
            console.error('Error scrolling to bottom: ', err);
        }
    }

    private findMessageById(id: number): ChatInternalMessages | undefined {
        return this.messages.find(message => +message.id === id);
    }

    private sortMessages() {
        this.messages.sort((a, b) => +a.id - +b.id);
    }

    private get allMessagesFetched(): boolean {
        if (this.firstMessageInDatabaseId === null || this.firstMessageInDatabaseId === undefined) {
            return false;
        }
        return this.messages.length > 0 ? this.firstMessageInDatabaseId === +this.messages[0].id : true;
    }

    private get lastMessageId(): number {
        return this.messages.length ? +this.messages[this.messages.length - 1].id : 0;
    }

    private setOpenSidebarStatus() {
        this._isMobileService.isLarge.pipe(take(1)).subscribe(isLarge => this.sidebarOpened = isLarge);
    }

    ngOnDestroy() {
        this.subscriptions?.unsubscribe();
    }

    ngAfterViewChecked() {
        if (!this.messages.length || !this.doScroll) {
            return;
        }

        if (this.doScroll === 'bottom') {
            this.scrollToBottom();
        } else if (this.doScroll === 'top') {
            this.scrollToEnableLoadMore();
        }

        this.doScroll = false;
    }

    private setSocketsSubscription() {
        if (this.lastThreadId) {
            this._socketChatInternalMessagesService.leave(this.lastThreadId);
        }

        if (this.subscription) {
            this.subscription?.unsubscribe();
        }

        this._socketChatInternalMessagesService.join(+this.thread.id);

        this.subscription = this._socketChatInternalMessagesService.messages$.subscribe(
            message => {
                if (+message.threadId === +this.thread.id && !this.findMessageById(+message.messageId)) {
                    this.loadRemainingMessages();
                }
            },
            err => this._messagingService.logToConsoleAndShowError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-SOCKET'), err)
        );

        this.lastThreadId = +this.thread.id;
    }

    get isUserBanned() {
        return !!+this.thread?.user?.banned;
    }

}
