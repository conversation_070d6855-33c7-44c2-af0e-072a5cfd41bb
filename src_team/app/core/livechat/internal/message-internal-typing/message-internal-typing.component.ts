import {Component, Input, OnInit} from '@angular/core';
import {UserStoreService} from '../../../../services/store/user-store.service';
import {switchMap, tap} from 'rxjs/operators';
import {SocketLivechatWfirmaMessagesService} from '../../../../services/sockets/socket-livechat-wfirma-messages.service';
import {Subscription, timer} from 'rxjs';
import {ApplicationSettingsService} from '../../../../services/application-settings.service';
import {SocketChatInternalMessagesService} from '../../../../services/sockets/socket-chat-internal-messages.service';
import { NgIf } from '@angular/common';
import { UserAvatarDisplayComponent } from '../../../../shared/user-avatar-display/user-avatar-display.component';

@Component({
    selector: 'app-message-internal-typing',
    templateUrl: './message-internal-typing.component.html',
    styleUrls: [
        '../../external/message/message.component.scss',
        './message-internal-typing.component.scss'
    ],
    imports: [NgIf, UserAvatarDisplayComponent]
})
export class MessageInternalTypingComponent implements OnInit {

    @Input() threadOwnerId: number;

    senderId: number = 0;
    sender;
    userTyping = false;
    subscription: Subscription;
    isReplyingTimeout = this.applicationSettingsService.getValue('CHAT_IS_REPLYING_TIMEOUT');

    constructor(
        public userStoreService: UserStoreService,
        private _socketChatInternalMessagesService: SocketChatInternalMessagesService,
        private applicationSettingsService: ApplicationSettingsService
    ) {
    }

    private setSocketTypingMessageSubscription() {
        this.subscription = this._socketChatInternalMessagesService.typingMessage$
            .pipe(
                tap((data: Object) => {
                    this.senderId = data['userId'];

                    this.userStoreService.getUserFromStoreTake1(this.senderId ).subscribe(sender => {
                        this.sender = sender['User'];
                    });

                    if (!this.userTyping) {
                        this.userTyping = true;
                    }
                }),
                switchMap(() => timer(this.isReplyingTimeout)),
            )
            .subscribe(() => {
                if (this.userTyping) {
                    this.userTyping = false;
                }
            });
    }

    ngOnInit() {
        this.setSocketTypingMessageSubscription();
    }

    ngOnDestroy() {
        this.subscription?.unsubscribe();
    }

}
