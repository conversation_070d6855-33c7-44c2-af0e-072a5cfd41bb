@use '../../../../../variables' as *;

$input-background-color: #fff;
$toolbar-height: 60px;

.chat-input {
    align-items: center;
    background-color: rgba($input-background-color, 0.10);
    display: flex;
    height: $livechat-wfirma-input-height;
    flex-direction: column;
    width: 100%;
}

.textarea {
    -ms-writing-mode: initial;
    -webkit-appearance: textarea;
    -webkit-rtl-ordering: logical;
    -webkit-writing-mode: initial;
    border-width: 15px 0 0 0;
    border-top: 15px solid $light-grey;
    cursor: text;
    display: inline-block;
    flex-grow: 1;
    font-size: 14px;
    letter-spacing: normal;
    line-height: 23px;
    min-width: 0;
    outline: none;
    overflow-wrap: break-word;
    overflow-y: auto;
    padding: 10px 30px 10px 20px;
    resize: none;
    text-align: start;
    text-indent: 0;
    text-rendering: auto;
    text-shadow: none;
    text-transform: none;
    white-space: pre-wrap;
    width: 100%;
    word-spacing: normal;
    writing-mode: initial;
}

.send-button {
    align-items: center;
    display: flex;
    height: $toolbar-height;
    justify-content: flex-end;
    padding: 0 20px;
    width: 100%;
}
