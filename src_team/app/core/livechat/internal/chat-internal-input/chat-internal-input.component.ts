import {Component, Input, OnChanges, SimpleChanges} from '@angular/core';
import {finalize} from 'rxjs/operators';
import {MessagingService} from '../../../../services/messaging.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {MessagesInternalService} from '../../../../services/livechat-wfirma/messages-internal.service';
import {SocketChatInternalMessagesService} from '../../../../services/sockets/socket-chat-internal-messages.service';
import { FormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';

@Component({
    selector: 'app-chat-internal-input',
    templateUrl: './chat-internal-input.component.html',
    styleUrls: ['./chat-internal-input.component.scss'],
    imports: [FormsModule, MatButton, TranslatePipe]
})
export class ChatInternalInputComponent implements OnChanges {
    @Input() threadId: number;

    sending = false;
    newMessageText = '';
    fileUploaderVisible = true;

    constructor(
        private _messagesInternalService: MessagesInternalService,
        private _messagingService: MessagingService,
        private _socketChatInternalMessagesService: SocketChatInternalMessagesService,
        public translate: TranslateService
    ) {
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['threadId']) {
            this.sending = false;
            this.newMessageText = '';
            this.fileUploaderVisible = true;
        }
    }

    messageReady() {
        return this.newMessageText && this.newMessageText.trim().length;
    }

    sendMessage() {
        if (!this.messageReady()) {
            return;
        }

        this.sending = true;

        const message: any = {
            chat_internal_thread_id: this.threadId,
            message: this.newMessageText,
            type: 'text',
        };

        this._messagesInternalService.postMessage(message)
            .pipe(finalize(() => this.sending = false))
            .subscribe(
                () => this.newMessageText = '',
                () => this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR'))
            );
    }

    onInputKeyDown(event: KeyboardEvent) {
        if (event.key.toUpperCase() !== 'ENTER') {
            this._socketChatInternalMessagesService.typing(+this.threadId);
        }
    }

    onUploadRefresh() {
        this.fileUploaderVisible = false;
        setTimeout(() => this.fileUploaderVisible = true, 100);
    }
}
