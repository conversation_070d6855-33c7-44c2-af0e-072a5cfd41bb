import {Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {SidebarsContainerService} from '../../../services/sidebars-container.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {ThreadsInternalService} from '../../../services/livechat-wfirma/threads-internal.service';
import {filter, map, startWith, switchMap} from 'rxjs/operators';
import {combineLatest, forkJoin, of, Subscription} from 'rxjs';
import {ChatInternalThread} from '../../../common/interfaces/chat-internal-thread.interface';
import {ChatInternalUserThread} from '../../../common/interfaces/chat-internal-user-thread.interface';
import {UserService} from '../../../services/user.service';
import {AuthService} from '../../../services/auth/auth.service';
import { ActivatedRoute, NavigationEnd, Router, RouterEvent, RouterLinkActive, RouterLink, RouterOutlet } from '@angular/router';
import {IsMobileService} from '../../../services/is-mobile.service';
import {MessagingService} from '../../../services/messaging.service';
import {MessagesInternalService} from '../../../services/livechat-wfirma/messages-internal.service';
import {ChatInternalMessages} from '../../../common/interfaces/chat-internal-messages.interface';
import {SocketChatInternalMessagesService} from '../../../services/sockets/socket-chat-internal-messages.service';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective, DefaultLayoutGapDirective } from 'ngx-flexible-layout/flex';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { NgIf, NgFor, SlicePipe, DatePipe } from '@angular/common';
import { MatNavList, MatListItem } from '@angular/material/list';
import { UserAvatarDisplayComponent } from '../../../shared/user-avatar-display/user-avatar-display.component';
import { EmptyInternalThreadComponent } from './empty-internal-thread/empty-internal-thread.component';

@Component({
    selector: 'app-internal',
    templateUrl: './internal.component.html',
    styleUrls: ['./internal.component.scss'],
    imports: [DefaultLayoutDirective, DefaultClassDirective, NgIf, MatNavList, NgFor, MatListItem, RouterLinkActive, RouterLink, DefaultLayoutAlignDirective, DefaultLayoutGapDirective, UserAvatarDisplayComponent, RouterOutlet, EmptyInternalThreadComponent, SlicePipe, DatePipe, TranslatePipe]
})
export class InternalComponent implements OnInit, OnDestroy {
    baseUrl = '/internal';
    threadsReady = false;
    selectedThreadVisible = false;
    threadsListVisible = false;
    threads: ChatInternalThread[] = [];
    subscriptions: Subscription = new Subscription();
    isXs: boolean;

    constructor(
        private sidebarsContainerService: SidebarsContainerService,
        public translate: TranslateService,
        private _threadsInternalService: ThreadsInternalService,
        private _messagesInternalService: MessagesInternalService,
        private _userService: UserService,
        private _authService: AuthService,
        private router: Router,
        private _isMobileService: IsMobileService,
        private _messagingService: MessagingService,
        private _route: ActivatedRoute,
        private _socketChatInternalMessagesService: SocketChatInternalMessagesService
    ) {
    }

    private setMainSubscription() {
        const currentUrl$ = this.router.events.pipe(
            filter((routerEvent): routerEvent is NavigationEnd => routerEvent instanceof NavigationEnd),
            map(navigationEnd => navigationEnd.url),
            startWith(this.router.url)
        );

        this.subscriptions.add(
            combineLatest([this._isMobileService.isXS, currentUrl$]).subscribe(
                ([isMobile, currentUrl]) => {
                    this.setVisibility(isMobile, currentUrl);
                    this.threadsReady = true;
                },
                () => this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-THREADS'))
            )
        );
    }

    private setVisibility(isXs: boolean, url: string) {
        this.isXs = isXs;

        if (isXs) {
            this.selectedThreadVisible = url !== this.baseUrl;
            this.threadsListVisible = !this.selectedThreadVisible;
        } else {
            this.selectedThreadVisible = true;
            this.threadsListVisible = true;

            if (url === this.baseUrl && this.threads[0]) {
                this.router.navigate(['thread', this.threads[0].id], {relativeTo: this._route});
            }
        }
    }

    ngOnInit() {
        this.getThreadsWithUsers();

        this.subscriptions.add(
            this._threadsInternalService.refreshThreads$.subscribe(data => {
                const threadToUpdate = this.threads.find(thread => thread.id === data[0]['chat_internal_thread_id']);

                threadToUpdate.lastMessage = data[0];

                this.threads = this.threads.sort((a: ChatInternalThread, b: ChatInternalThread) => {
                    const dateA = a.lastMessage ? new Date(a.lastMessage.modified).getTime() : 0;
                    const dateB = b.lastMessage ? new Date(b.lastMessage.modified).getTime() : 0;

                    return dateB - dateA;
                });
            })
        );
    }

    getThreadsWithUsers() {
        this._socketChatInternalMessagesService.threads$
            .pipe(
                startWith(true),
                switchMap(() => this._threadsInternalService.getThreads()
                    .pipe(
                        switchMap(threads => {
                            if (!threads || threads.length === 0) {
                                return of([]);
                            }

                            const threadObservables = threads.map((thread: ChatInternalThread) => {
                                return this._threadsInternalService.getUserThread(thread.id)
                                    .pipe(
                                        switchMap(usersThread => {
                                            if (usersThread) {
                                                const userThread = usersThread.filter((ut: ChatInternalUserThread) =>
                                                    +ut.user_id !== +this._authService.getUserId()
                                                );

                                                const ownerThread = usersThread.filter((ut: ChatInternalUserThread) =>
                                                    +ut.is_owner === 1
                                                );

                                                thread.owner_id = ownerThread?.[0]?.user_id || null;

                                                if (userThread.length === 0) {
                                                    thread.user = null;
                                                    thread.messages = [];
                                                    thread.lastMessage = null;

                                                    return of(thread);
                                                }

                                                const selectedUserId = userThread?.[0]?.user_id || usersThread?.[0]?.user_id;

                                                return this._userService.getUser(selectedUserId)
                                                    .pipe(
                                                        switchMap(user => {
                                                            thread.user = user;

                                                            return this._messagesInternalService.getMessages(thread.id, null, 'desc', 9999)
                                                                .pipe(
                                                                    map((messages: ChatInternalMessages[]) => {
                                                                        thread.messages = messages;

                                                                        let lastMessage = null;

                                                                        if (messages && messages.length > 0) {
                                                                            lastMessage = messages.reduce((latest, message) => {
                                                                                return new Date(message.modified) > new Date(latest.modified) ? message : latest;
                                                                            }, messages[0]);

                                                                            thread.lastMessage = lastMessage;
                                                                        }

                                                                        return thread;
                                                                    })
                                                                );
                                                        })
                                                    );
                                            }
                                        })
                                    );
                            });

                            return forkJoin(threadObservables);
                        })
                    )
                )
            )
            .subscribe(
                updatedThreads => {
                    this.threads = updatedThreads;

                    this.threads = updatedThreads.sort((a, b) => {
                        const dateA = a.lastMessage ? new Date(a.lastMessage.modified).getTime() : 0;
                        const dateB = b.lastMessage ? new Date(b.lastMessage.modified).getTime() : 0;

                        return dateB - dateA;
                    });

                    this.setMainSubscription();
                },
                error => {
                    console.error('Error fetching threads with users and messages:', error);
                }
            );
    }

    ngOnDestroy(): void {
        this.setMainSubscription();
    }
}
