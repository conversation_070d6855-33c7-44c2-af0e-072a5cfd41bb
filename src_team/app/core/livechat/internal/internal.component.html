<div class="livechat-wfirma" fxLayout="row" ngClass.xs="xs">
    <mat-nav-list [disableRipple]="true" class="threads-list" *ngIf="threadsListVisible">
        <ng-container *ngIf="threads.length > 0; else emptyThreadList">
            <a
                *ngFor="let thread of threads"
                mat-list-item
                routerLink="thread/{{thread.id}}"
                routerLinkActive="active"
                class="thread">
                <div class="thread-overview">
                    <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="4px">
                        <div fxLayout fxLayoutAlign="start start" fxLayoutGap="10px">
                            <div>
                                <app-user-avatar-display
                                        matListAvatar
                                        class="settings-avatar"
                                        [userId]="thread?.user?.id">
                                </app-user-avatar-display>
                            </div>
                            <div fxLayout="column wrap"  fxLayoutGap="4px">
                                <span class="thread-name">{{ thread?.user?.firstname }} {{ thread?.user?.lastname }}
                                    <ng-container *ngIf="+thread?.user?.banned">
                                        <span>({{'LIVECHAT-WFIRMA.USER-BLOCKED' | translate}})</span>
                                    </ng-container>
                                </span>
<!--                                [ngClass]="thread.lastMessage?.is_read ? 'is-read' : ''-->
                                <div class="last-message">
                                    {{thread?.lastMessage?.message | slice:0:50}}{{ thread?.lastMessage?.message.length > 50 ? '...' : '' }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <span class="thread-date">{{thread?.lastMessage?.modified | date : 'dd MMM yyyy, HH:mm'}}</span>
                </div>
            </a>
        </ng-container>
        <ng-template #emptyThreadList>
            <div class="empty-thread-list" *ngIf="threadsReady">
                <img src="/src_team/../../assets/images/empty-thread-list.svg" alt="empty thread list">
                <div class="text">
                    {{'LIVECHAT-WFIRMA.EMPTY-THREADS' | translate}}<br>{{'LIVECHAT-WFIRMA.EMPTY-THREADS-2' | translate}}
                </div>
            </div>
        </ng-template>
    </mat-nav-list>

    <div class="thread-container" *ngIf="selectedThreadVisible">
        <ng-container *ngIf="threads.length > 0; else emptyThread">
            <router-outlet></router-outlet>
        </ng-container>
    </div>

    <ng-template #emptyThread>
        <app-empty-internal-thread></app-empty-internal-thread>
    </ng-template>
</div>
