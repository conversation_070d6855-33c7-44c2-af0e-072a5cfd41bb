@use '../../../../variables' as *;

:host ::ng-deep .mat-list-item-content {
    width: 100%;
}

.livechat-wfirma {
    margin: 10px;
    height: calc(100% - 64px - 64px - 20px); // 64px => app-header, 64px => app-top-menu, 20px => app-livrechat .livechat-wfirma

    &.xs .threads-list {
        margin-right: 0;
    }

    .empty-thread-list {
        align-items: center;
        display: flex;
        height: 100%;
        justify-content: center;

        .text {
            color: #E2E8ED;
            font-size: 25px;
            line-height: 34px;
            margin-left: 15px;
            font-weight: 500;
        }

        @media (max-width: 1500px) {
            flex-flow: column;

            .text {
                font-size: 20px;
                line-height: 30px;
                text-align: center;
                margin: 15px 0 0 0;
            }
        }
    }

    .threads-list {
        background-color: var(--background-color);
        border-radius: $container-border-radius;
        height: 100%;
        margin-right: 12px;
        min-width: 200px;
        overflow: hidden;
        overflow-y: auto;
        padding-top: 0;
        width: 40%;

        ::ng-deep .mat-list-item-content {
            border-bottom: 0.5px solid rgba(193, 193, 193, 0.5) !important;
            margin: 0 17px;
            padding: 0 !important;
        }

        .mat-list-item.active {
            background-color: $hover !important;
        }

        .thread {
            align-items: center;
            display: flex;
            height: 110px;
            justify-content: center;
            width: 100%;

            .thread-overview {
                display: flex;
                flex-direction: column;
                width: 100%;

                .thread-name {
                    color: #1270B6;
                    font-size: 16px;
                    font-weight: 500;
                }

                .thread-date {
                    align-self: flex-end;
                    color: $light-medium-grey;
                    white-space: nowrap;
                    font-size: 12px;
                }

                .last-message {
                    font-size: 14px;
                    margin: 6px 0;
                    overflow: hidden;
                    width: 100%;
                }

                .is-read {
                    font-weight: bold;
                }

                &.active .last-message {
                    font-weight: 500;
                }

                &.inactive .last-message {
                    font-weight: 400;
                }
            }
        }
    }

    .thread-container {
        background-color: var(--background-color);
        border-radius: $container-border-radius;
        width: 75%;
    }

    &.xs {
        .threads-list {
            width: 100%;
        }

        .thread-container {
            bottom: 0;
            height: 100%;
            left: 0;
            position: absolute;
            width: 100%;
        }
    }
}
