import {Component, Input, OnInit} from '@angular/core';
import {UserStoreService} from '../../../../services/store/user-store.service';
import {AvatarStoreService} from '../../../../services/store/avatar-store.service';
import {activateLinks} from '../../../../common/utils/activate-links';
import {Dom<PERSON>anitizer, SafeHtml} from '@angular/platform-browser';
import {ChatInternalMessages} from '../../../../common/interfaces/chat-internal-messages.interface';
import { NgIf, NgClass, DatePipe } from '@angular/common';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { UserAvatarDisplayComponent } from '../../../../shared/user-avatar-display/user-avatar-display.component';
import { MatIcon } from '@angular/material/icon';
import { MatTooltip } from '@angular/material/tooltip';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-message-internal',
    templateUrl: './message-internal.component.html',
    styleUrls: ['./message-internal.component.scss'],
    imports: [NgIf, NgClass, DefaultClassDirective, UserAvatarDisplayComponent, MatIcon, MatTooltip, DatePipe, TranslatePipe]
})
export class MessageInternalComponent implements OnInit {
    @Input() message: ChatInternalMessages;
    @Input() threadOwnerId: number;

    messageShown: SafeHtml;

    constructor(
        public userStoreService: UserStoreService,
        private sanitizer: DomSanitizer
    ) {
    }

    ngOnInit() {
        this.messageShown = this.sanitizer.bypassSecurityTrustHtml(activateLinks(this.message.message));
    }

    getClasses() {
        return this.message?.user_type === 'loggedUser' ? 'loggedUser' : 'user';
    }
}
