<div *ngIf="message" class="chat-message" [ngClass]="getClasses()">
    <div class="chat-message__sender">
        <app-user-avatar-display class="chat-message__avatar" [userId]="message.user_id"></app-user-avatar-display>
    </div>
    <div>
        <div class="chat-message__text" [innerHTML]="messageShown"></div>
        <div class="chat-message chat-message--date {{message.user_type}}">
            <mat-icon class="chat-message__read-icon" *ngIf="+message.is_read" matTooltip="{{'LIVECHAT-WFIRMA.IS-READ' | translate}}">check_circle</mat-icon>
            <span class="chat-message__datetime">{{message.created | date : 'yyyy-MM-dd HH:mm'}}</span>
        </div>
    </div>
</div>
