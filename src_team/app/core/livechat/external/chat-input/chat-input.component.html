<div class="chat-input">
        <textarea
            class="textarea"
            placeholder="{{'LIVECHAT-WFIRMA.PLACEHOLDER' | translate}}"
            [(ngModel)]="newMessageText"
            (keydown)="onInputKeyDown($event)"
            (keydown.enter)="sendMessage()"
        ></textarea>
    <div class="toolbar">
        <app-file-uploader
            *ngIf="fileUploaderVisible"
            [threadId]="threadId"
            [enabled]="true"
            (refresh)="onUploadRefresh()"
        >
        </app-file-uploader>
        <button
                mat-stroked-button
                class="button-rounded primary"
                color="primary"
                [disabled]="!messageReady() || sending"
                (click)="sendMessage()"
        >
            {{'LIVECHAT-WFIRMA.SEND' | translate}}
        </button>
    </div>
</div>
