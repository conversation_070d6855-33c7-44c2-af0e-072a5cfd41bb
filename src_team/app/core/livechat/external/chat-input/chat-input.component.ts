import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {finalize} from 'rxjs/operators';
import {MessagesService} from '../../../../services/livechat-wfirma/messages.service';
import {MessagingService} from '../../../../services/messaging.service';
import {SocketLivechatWfirmaMessagesService} from '../../../../services/sockets/socket-livechat-wfirma-messages.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';
import { FileUploaderComponent } from '../file-uploader/file-uploader.component';
import { MatButton } from '@angular/material/button';

@Component({
    selector: 'app-chat-input',
    templateUrl: './chat-input.component.html',
    styleUrls: ['./chat-input.component.scss'],
    imports: [FormsModule, NgIf, FileUploaderComponent, MatButton, TranslatePipe]
})
export class ChatInputComponent implements OnChanges {
    @Input() threadId: number;

    sending = false;
    newMessageText = '';
    fileUploaderVisible = true;

    constructor(
        private messagesService: MessagesService,
        private messagingService: MessagingService,
        private socketLivechatWfirmaMessagesService: SocketLivechatWfirmaMessagesService,
        public translate: TranslateService
    ) {
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['threadId']) {
            this.sending = false;
            this.newMessageText = '';
            this.fileUploaderVisible = true;
        }
    }

    messageReady() {
        return this.newMessageText && this.newMessageText.trim().length;
    }

    sendMessage() {
        if (!this.messageReady()) {
            return;
        }

        this.sending = true;

        const message: any = {
            chat_thread_id: this.threadId,
            message: this.newMessageText,
            type: 'text',
        };

        this.messagesService.postMessage(message)
            .pipe(finalize(() => this.sending = false))
            .subscribe(
                () => this.newMessageText = '',
                () => this.messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR'))
            );
    }

    onInputKeyDown(event: KeyboardEvent) {
        if (event.key.toUpperCase() !== 'ENTER') {
            this.socketLivechatWfirmaMessagesService.typing(+this.threadId);
        }
    }

    onUploadRefresh() {
        this.fileUploaderVisible = false;
        setTimeout(() => this.fileUploaderVisible = true, 100);
    }
}
