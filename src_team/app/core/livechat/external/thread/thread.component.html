<ng-container *ngIf="messages && thread && clientUserName; else loadingMessage;">
    <div class="chat-thread" [ngClass]="{'live-chat-dark-mode': darkMode}" ngClass.xs="xs">
        <mat-drawer-container>
            <mat-drawer-content>
                <ng-container>
                    <div class="thread-header">
                        <a mat-button routerLink="/chat" fxHide.gt-xs>
                            <mat-icon>arrow_back</mat-icon>
                        </a>
                        <div class="thread-info" *ngIf="thread">
                            <span fxLayout fxLayoutAlign="space-between">
                                <span class="client-name"> {{clientUserName || ('LIVECHAT-WFIRMA.ANONYMOUS' | translate)}}</span>
                            </span>
                        </div>
                    </div>
                </ng-container>
                <hr class="ruler">
                <div class="messages-list" #messageList (scroll)="onScroll($event)">
                    <ng-container *ngFor="let message of messages; let lastMessage = last">
                        <ng-container [ngSwitch]="message.type">
                            <ng-container *ngSwitchCase="'text'">
                                <app-message
                                        [message]="message"
                                        [threadOwnerId]="thread.owner_id"
                                        [clientUserName]="clientUserName"
                                ></app-message>
                            </ng-container>
                            <ng-container *ngSwitchCase="'file'">
                                <app-message-file
                                        [message]="message"
                                        [threadOwnerId]="thread.ownerId"
                                ></app-message-file>
                            </ng-container>
                        </ng-container>
                        <app-message-typing
                                *ngIf="lastMessage"
                                [threadOwnerId]="thread?.owner_id"
                                [clientUserName]="clientUserName">
                        </app-message-typing>
                    </ng-container>
                    <ng-container *ngIf="thread?.status === 'closed'" class="thread-closed">
                        <hr class="ruler">
                        <span>{{'LIVECHAT-WFIRMA.ENDED' | translate}}</span>
                    </ng-container>
                </div>
                <app-chat-input [threadId]="thread?.id"></app-chat-input>
            </mat-drawer-content>
        </mat-drawer-container>
    </div>
</ng-container>

<ng-template #loadingMessage>
    <div class="spinner-wrapper">
        <mat-spinner></mat-spinner>
    </div>
</ng-template>
