import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, OnInit, ViewChild} from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {distinctUntilChanged, map, switchMap, take} from 'rxjs/operators';
import {MessagesService} from '../../../../services/livechat-wfirma/messages.service';
import {ChatMessage} from '../../../../common/interfaces/chat-message.interface';
import {MessagingService} from '../../../../services/messaging.service';
import {combineLatest, forkJoin, Subject, Subscription} from 'rxjs';
import {ThreadsService} from '../../../../services/livechat-wfirma/threads.service';
import {ChatThread} from '../../../../common/interfaces/chat-thread.interface';
import {SocketLivechatWfirmaMessagesService} from '../../../../services/sockets/socket-livechat-wfirma-messages.service';
import {SocketsService} from '../../../../services/sockets/sockets.service';
import {ApplicationSettingsService} from '../../../../services/application-settings.service';
import {IsMobileService} from '../../../../services/is-mobile.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {UserService} from '../../../../services/user.service';
import {UserStoreService} from '../../../../services/store/user-store.service';
import {ChatUsersService} from '../../../../services/livechat-wfirma/chat-users.service';
import {TourService} from 'ngx-ui-tour-md-menu';
import { NgIf, NgClass, NgFor, NgSwitch, NgSwitchCase } from '@angular/common';
import { DefaultClassDirective, DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { MatDrawerContainer, MatDrawerContent } from '@angular/material/sidenav';
import { MatAnchor } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { MessageComponent } from '../message/message.component';
import { MessageFileComponent } from '../message-file/message-file.component';
import { MessageTypingComponent } from '../message-typing/message-typing.component';
import { ChatInputComponent } from '../chat-input/chat-input.component';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-thread',
    templateUrl: './thread.component.html',
    styleUrls: ['./thread.component.scss'],
    imports: [NgIf, DefaultClassDirective, NgClass, MatDrawerContainer, MatDrawerContent, MatAnchor, RouterLink, DefaultShowHideDirective, MatIcon, DefaultLayoutDirective, DefaultLayoutAlignDirective, NgFor, NgSwitch, NgSwitchCase, MessageComponent, MessageFileComponent, MessageTypingComponent, ChatInputComponent, MatProgressSpinner, TranslatePipe]
})
export class ThreadComponent implements OnInit, OnDestroy, AfterViewChecked {
    @ViewChild('messageList') private messageList: ElementRef;

    thread: ChatThread;
    messages: ChatMessage[] = [];
    sidebarOpened = false;
    clientName = '';
    clientCompanyName = '';
    drawerMode = 'side';

    private messagesFetched$ = new Subject();
    private lastThreadId: number;
    private subscription: Subscription;
    private subscriptions = new Subscription();

    private loading: false | 'previous' | 'next' = 'next';
    private doScroll: false | 'top' | 'bottom' = false;
    private lastScrollPosition = Number.MAX_SAFE_INTEGER;
    private CHAT_MESSAGE_PACKET_SIZE: number;
    private SCROLL_LOAD_POSITION = 50;
    private firstMessageInDatabaseId: number;
    public darkMode: boolean = false;
    public clientUserName: string;
    public wait = false;

    constructor(
        private route: ActivatedRoute,
        private messagesService: MessagesService,
        private threadsService: ThreadsService,
        private messagingService: MessagingService,
        private socketLivechatWfirmaMessagesService: SocketLivechatWfirmaMessagesService,
        private socketsService: SocketsService,
        private applicationSettingsService: ApplicationSettingsService,
        private isMobileService: IsMobileService,
        public translate: TranslateService,
        private ChatUserService: ChatUsersService,
        private tourService: TourService
    ) {
        this.CHAT_MESSAGE_PACKET_SIZE = this.applicationSettingsService.getValue('CHAT_MESSAGE_PACKET_SIZE');
    }

    ngOnInit() {
        this.darkMode = !!this.applicationSettingsService.getValue('darkMode');
        const routeParams$ = this.route.params.pipe(map(params => +params.id));

        this.subscriptions.add(
            this.isMobileService.isLarge.subscribe(isXl => this.drawerMode = isXl ? 'side' : 'over')
        );

        this.subscriptions.add(
            routeParams$.subscribe(threadId => {
                this.loadMessagesFirstTime(threadId);
            })
        );

        this.subscriptions.add(
            combineLatest([
                routeParams$.pipe(distinctUntilChanged()),
                this.socketsService.connected$,
                this.messagesFetched$.pipe(distinctUntilChanged())
            ]).subscribe(
                ([threadId, socketsConnected, messagesFetched]) => {
                    if (threadId && socketsConnected && messagesFetched) {
                        this.setSocketsSubscription();
                    }
                }
            )
        );
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription?.unsubscribe();
        }

        this.subscriptions?.unsubscribe();
        this.socketLivechatWfirmaMessagesService.leave(+this.thread.id);
    }

    ngAfterViewChecked() {
        if (!this.messages.length || !this.doScroll) {
            return;
        }

        if (this.doScroll === 'bottom') {
            this.scrollToBottom();
        } else if (this.doScroll === 'top') {
            this.scrollToEnableLoadMore();
        }

        this.doScroll = false;
    }

    private get allMessagesFetched() {
        return this.messages.length
            ? this.firstMessageInDatabaseId === +this.messages[0].id
            : true;
    }

    private addMessages(messages: ChatMessage[]) {
        for (const message of messages) {
            if (!this.findMessageById(+message.id)) {
                this.messages.push(message);
            }
        }
    }

    private sortMessages() {
        this.messages = this.messages.sort((a, b) => +a.id - +b.id);
    }

    private get lastMessageId() {
        const length = this.messages.length;

        return length ? +this.messages[this.messages.length - 1].id : 0;
    }

    private scrollToBottom() {
       setTimeout(() => this.messageList.nativeElement.scrollTop = this.messageList.nativeElement.scrollHeight, 500);
    }

    private findMessageById(id: number) {
        return this.messages.find(message => +message.id === id);
    }

    private setSocketsSubscription() {
        if (this.lastThreadId) {
            this.socketLivechatWfirmaMessagesService.leave(this.lastThreadId);
        }

        if (this.subscription) {
            this.subscription?.unsubscribe();
        }

        this.socketLivechatWfirmaMessagesService.join(+this.thread.id);

        this.subscription = this.socketLivechatWfirmaMessagesService.messages$.subscribe(
            message => {
                if (+message.threadId === +this.thread.id && !this.findMessageById(+message.messageId)) {
                    this.loadRemainingMessages();
                }
            },
            err => this.messagingService.logToConsoleAndShowError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-SOCKET'), err)
        );

        this.lastThreadId = +this.thread.id;
    }

    private checkScrollDirectionUp(position: number) {
        const result = position < this.lastScrollPosition;

        this.lastScrollPosition = position;

        return result;
    }

    private reachedTop(scrollbarPosition) {
        return scrollbarPosition < this.SCROLL_LOAD_POSITION;
    }

    private setScrollPosition() {
        if (this.loading === 'previous') {
            this.lastScrollPosition = 0;

            if (!this.allMessagesFetched) {
                this.doScroll = 'top';
            }
        } else {
            this.lastScrollPosition = Number.MAX_SAFE_INTEGER;
            this.doScroll = 'bottom';
        }

        this.loading = false;
    }

    private scrollToEnableLoadMore() {
        setTimeout(() => this.messageList.nativeElement.scrollTop = 300, 300);
    }

    private getMessageById(id: number) {
        return this.messages.find(message => +message.id === id);
    }

    private addNewMessages(messages: ChatMessage[]) {
        for (const message of messages) {
            if (+message.chat_thread_id === +this.thread.id && !this.getMessageById(+message.id)) {
                this.messages.push(message);
            }
        }

        this.sortMessages();
    }

    private loadPreviousMessages() {
        if (this.allMessagesFetched) {
            return;
        }

        this.messagesService.getMessages(+this.thread.id, +this.messages[0].id, 'desc', this.CHAT_MESSAGE_PACKET_SIZE).subscribe(
            result => {
                this.setScrollPosition();
                this.addNewMessages(result);
            },
            () => {
                this.messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-DOWNLOADING'));
            }
        );
    }

    private loadRemainingMessages() {
        this.messagesService.getMessages(+this.thread.id, this.lastMessageId, 'asc', 9999).subscribe(
            messages => {
                this.addMessages(messages);
                this.sortMessages();
                this.scrollToBottom();
            }
        );
    }

    private decodeClientInfo(clientInfo: string = ''): any[] {
        if (!clientInfo) {
            return;
        }

        try {
            const decoded = JSON.parse(atob(clientInfo));

            return Object.keys(decoded).map(key => ({key, value: decoded[key]}));
        } catch (e) {
            return [];
        }
    }

    private setClientName() {
        if (!this.thread.clientData) {
            return;
        }

        for (const data of this.thread.clientData) {
            if (data.key === this.translate.instant('LIVECHAT-WFIRMA.BASIC-DATA')) {
                this.clientCompanyName = data.value[this.translate.instant('LIVECHAT-WFIRMA.COMPANY-NAME')];
                this.clientName = data.value[this.translate.instant('LIVECHAT-WFIRMA.NAME')];
            }
        }
    }

    private setOpenSidebarStatus() {
        this.isMobileService.isLarge.pipe(take(1)).subscribe(isLarge => this.sidebarOpened = isLarge);
    }

    private loadMessagesFirstTime(threadId: number) {
        forkJoin([
            this.messagesService.getFirstMessageInThreadId(threadId),
            this.messagesService.getMessages(threadId, null, 'desc', this.CHAT_MESSAGE_PACKET_SIZE),
            this.threadsService.getThreads([threadId])
        ]).subscribe(([firstMessageId, messages, threads]) => {
                this.ChatUserService.getChatUserByUserId(threads[0].user_client_id).subscribe(chatUser => {
                    this.clientUserName = chatUser[0].user_name;
                });

                this.firstMessageInDatabaseId = firstMessageId;
                this.messages = messages;
                this.sortMessages();
                this.thread = threads[0];
                this.thread.clientData = this.decodeClientInfo('' + this.thread.client_info);
                this.setClientName();
                this.messagesFetched$.next(this.thread.id);
                this.setScrollPosition();
                this.setOpenSidebarStatus();
                setTimeout(() => this.wait = true, 500);
            },
            () => {
                this.messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-MESSAGES'));
                this.messages = [];
                this.thread = null;
            }
        );
    }

    onScroll(event: any) {
        if (this.loading) {
            return;
        }

        const scrollbarPosition = +event.target.scrollTop,
            scrollUP = this.checkScrollDirectionUp(scrollbarPosition),
            reachedTop = this.reachedTop(scrollbarPosition);

        if (scrollUP && reachedTop) {
            this.loading = 'previous';
            this.loadPreviousMessages();
        }
    }
}
