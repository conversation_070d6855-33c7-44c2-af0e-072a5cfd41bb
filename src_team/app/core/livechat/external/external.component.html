<div class="livechat-wfirma" fxLayout="row" ngClass.xs="xs">
    <mat-nav-list [disableRipple]="true" class="threads-list" *ngIf="threadsListVisible">
        <ng-container *ngIf="activeThreads.length > 0; else emptyThreadList">
            <a
                *ngFor="let thread of activeThreads"
                mat-list-item
                routerLink="thread/{{thread.chat_thread_id}}"
                routerLinkActive="active"
                class="thread"
            >
                <div class="thread-overview" ngClass="{{thread.chat_thread_status}}">
                    <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="4px">
                        <div fxLayout fxLayoutAlign="start center">
                            <span class="thread-name">{{thread.user_firstname}}</span>
                        </div>
                    </div>
                    <div class="last-message">
                        <mat-icon *ngIf="thread.chat_message_type === 'file'" class="attachment-ico">attachment</mat-icon>
                        <span *ngIf="thread.chat_message_type === 'file'">{{'LIVECHAT-WFIRMA.ATTACHMENT' | translate}}</span>
                        {{thread.chat_thread_last_message | slice:0:50}}{{ thread.chat_thread_last_message.length > 50 ? '...' : '' }}
                    </div>
                    <span class="thread-date">{{thread.chat_thread_modified | date : 'yyyy-MM-dd HH:mm'}}</span>
                </div>
            </a>
        </ng-container>
        <ng-template #emptyThreadList>
            <div class="empty-thread-list" *ngIf="threadsReady">
                <img src="/src_team/../assets/images/empty-thread-list.svg" alt="empty thread list">
                <div class="text">
                    {{'LIVECHAT-WFIRMA.EMPTY-THREADS' | translate}}<br>{{'LIVECHAT-WFIRMA.EMPTY-THREADS-2' | translate}}
                </div>
            </div>
        </ng-template>
    </mat-nav-list>

    <div class="thread-container" *ngIf="selectedThreadVisible">
        <ng-container *ngIf="activeThreads.length > 0; else emptyThread">
            <router-outlet></router-outlet>
        </ng-container>
    </div>

    <ng-template #emptyThread>
        <app-empty-thread></app-empty-thread>
    </ng-template>
</div>
