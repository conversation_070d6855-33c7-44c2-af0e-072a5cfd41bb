import {Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {ThreadsService} from '../../../services/livechat-wfirma/threads.service';
import { ActivatedRoute, NavigationEnd, Router, RouterEvent, RouterLinkActive, RouterLink, RouterOutlet } from '@angular/router';
import {MessagingService} from '../../../services/messaging.service';
import {filter, map, startWith, switchMap} from 'rxjs/operators';
import {IsMobileService} from '../../../services/is-mobile.service';
import {combineLatest, Subscription} from 'rxjs';
import {SidebarsContainerService} from '../../../services/sidebars-container.service';
import {ChatOpenThread} from '../../../common/interfaces/chat-open-thread.interface';
import {SocketLivechatWfirmaMessagesService} from '../../../services/sockets/socket-livechat-wfirma-messages.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective, DefaultLayoutGapDirective } from 'ngx-flexible-layout/flex';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { NgIf, NgFor, NgClass, SlicePipe, DatePipe } from '@angular/common';
import { MatNavList, MatListItem } from '@angular/material/list';
import { MatIcon } from '@angular/material/icon';
import { EmptyThreadComponent } from './empty-thread/empty-thread.component';

@Component({
    selector: 'app-livechat',
    templateUrl: './external.component.html',
    styleUrls: ['./external.component.scss'],
    imports: [DefaultLayoutDirective, DefaultClassDirective, NgIf, MatNavList, NgFor, MatListItem, RouterLinkActive, RouterLink, NgClass, DefaultLayoutAlignDirective, DefaultLayoutGapDirective, MatIcon, RouterOutlet, EmptyThreadComponent, SlicePipe, DatePipe, TranslatePipe]
})
export class ExternalComponent implements OnInit, OnDestroy {

    baseUrl = '/chat';
    activeThreads: ChatOpenThread[] = [];
    threadsReady = false;
    selectedThreadVisible = false;
    threadsListVisible = true;
    isXs: boolean;
    subscriptions = new Subscription();
    threads$ = this.socketLivechatWfirmaMessagesService.threads$.pipe(
        startWith(true),
        switchMap(() => this.threadsService.getOpenThreads().pipe(
            map(threads => threads.sort((a, b) => {
                const dateA = new Date(a.chat_thread_modified).getTime();
                const dateB = new Date(b.chat_thread_modified).getTime();

                return dateB - dateA; // Sortowanie malejąco
            }))
        ))
    );

    constructor(
        private threadsService: ThreadsService,
        private router: Router,
        private route: ActivatedRoute,
        private messagingService: MessagingService,
        private isMobileService: IsMobileService,
        private sidebarsContainerService: SidebarsContainerService,
        private socketLivechatWfirmaMessagesService: SocketLivechatWfirmaMessagesService,
        public translate: TranslateService
    ) {
    }

    private setVisibility(isXs: boolean, url: string) {
        this.isXs = isXs;

        if (isXs) {
            this.selectedThreadVisible = url !== this.baseUrl;
            this.threadsListVisible = !this.selectedThreadVisible;
        } else {
            this.selectedThreadVisible = true;
            this.threadsListVisible = true;

            if (url === this.baseUrl && this.activeThreads[0]) {
                this.router.navigate(['thread', this.activeThreads[0].chat_thread_id], {relativeTo: this.route});
            }
        }
    }

    private setMainSubscription() {
        const currentUrl$ = this.router.events.pipe(
            filter((routerEvent): routerEvent is NavigationEnd => routerEvent instanceof NavigationEnd),
            map(navigationEnd => navigationEnd.url),
            startWith(this.router.url)
        );

        this.subscriptions.add(
            combineLatest([this.isMobileService.isXS, currentUrl$, this.threads$]).subscribe(
                ([isMobile, currentUrl, threads]) => {
                    this.activeThreads = threads;
                    this.setVisibility(isMobile, currentUrl);
                    this.threadsReady = true;
                },
                () => this.messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-THREADS'))
            )
        );
    }

    ngOnInit() {
        this.setMainSubscription();
    }

    ngOnDestroy() {
        this.subscriptions?.unsubscribe();
    }
}
