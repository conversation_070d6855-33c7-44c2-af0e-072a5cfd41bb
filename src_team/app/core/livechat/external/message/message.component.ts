import {Component, Input, OnInit} from '@angular/core';
import {ChatMessage} from '../../../../common/interfaces/chat-message.interface';
import {UserStoreService} from '../../../../services/store/user-store.service';
import {AvatarStoreService} from '../../../../services/store/avatar-store.service';
import {IsMobileService} from '../../../../services/is-mobile.service';
import {UserAvatarService} from '../../../../services/user-avatar.service';
import {activateLinks} from '../../../../common/utils/activate-links';
import {DomSanitizer, SafeHtml} from '@angular/platform-browser';
import {MatDialog} from '@angular/material/dialog';
import { NgIf, NgClass, DatePipe } from '@angular/common';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { AvatarModule } from 'ngx-avatars';
import { MatTooltip } from '@angular/material/tooltip';
import { UserAvatarDisplayComponent } from '../../../../shared/user-avatar-display/user-avatar-display.component';
import { MatIcon } from '@angular/material/icon';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-message',
    templateUrl: './message.component.html',
    styleUrls: ['./message.component.scss'],
    imports: [NgIf, NgClass, DefaultClassDirective, AvatarModule, MatTooltip, UserAvatarDisplayComponent, MatIcon, DatePipe, TranslatePipe]
})
export class MessageComponent implements OnInit {
    @Input() message: ChatMessage;
    @Input() threadOwnerId: number;
    @Input() clientUserName: string;

    messageShown: SafeHtml;
    sender: any = {};

    constructor(
        public userStoreService: UserStoreService,
        private avatarStoreService: AvatarStoreService,
        private dialog: MatDialog,
        private isMobileService: IsMobileService,
        private userAvatarService: UserAvatarService,
        private sanitizer: DomSanitizer
    ) {
    }

    private setDefaultAvatar() {
        this.avatarStoreService.defaultAvatar$.subscribe(
            defaultAvatar => this.sender.avatar = defaultAvatar
        );
    }

    private getEmployeeData() {
        this.userStoreService.getUserFromStoreTake1(+this.message.user_id).subscribe(
            sender => this.sender = sender
        );
    }

    ngOnInit() {
        if (+this.message.is_reply) {
            this.getEmployeeData();
        }

        this.messageShown = this.sanitizer.bypassSecurityTrustHtml(activateLinks(this.message.message));
    }

    getClasses() {
        return +this.message.is_reply ? 'employee' : 'user';
    }
}
