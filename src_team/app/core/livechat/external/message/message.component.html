<div *ngIf="message" class="chat-message" [ngClass]="getClasses()">
    <div class="chat-message__sender">
        <ng-container *ngIf="!+message.is_reply; else employeeAvatar">
            <ngx-avatars matTooltip="{{clientUserName}}" [size]="40" [name]="clientUserName"></ngx-avatars>
        </ng-container>
        <ng-template #employeeAvatar>
            <ng-container *ngIf="sender.User.has_avatar; else employeeDefaultAvatar">
                <app-user-avatar-display
                        [userId]="+sender.User.id"
                        [size]="40"
                        matTooltip="{{sender.User.firstname + ' ' + sender.User.lastname}}">
                </app-user-avatar-display>
            </ng-container>
            <ng-template #employeeDefaultAvatar>
                <ngx-avatars
                    [size]="40"
                    [name]="sender.User.firstname + ' ' + sender.User.lastname"
                    matTooltip="{{sender.User.firstname + ' ' + sender.User.lastname}}">
                </ngx-avatars>
            </ng-template>
        </ng-template>
    </div>
    <div>
        <div class="chat-message__text" [innerHTML]="messageShown"></div>
        <div class="chat-message chat-message--date {{message.type}}">
            <mat-icon class="chat-message__read-icon" *ngIf="+message.is_read" matTooltip="{{'LIVECHAT-WFIRMA.IS-READ' | translate}}">check_circle</mat-icon>
            <span class="chat-message__datetime">{{message.created | date : 'yyyy-MM-dd HH:mm'}}</span>
        </div>
    </div>
</div>
