<div class="chat-message-typing">
    <div *ngIf="userTyping"  class="chat-message user">
        <ng-container>
            <ngx-avatars matTooltip="{{clientUserName}}" [size]="40" [name]="clientUserName"></ngx-avatars>
        </ng-container>
        <div class="chat-message__text">
            <div class="chat-message__loading">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </div>
</div>

<ng-template #defaultAvatarTemplate>
    <img src="/src_team/assets/images/default-avatar.svg" alt="avatar" class="chat-message__avatar">
</ng-template>
