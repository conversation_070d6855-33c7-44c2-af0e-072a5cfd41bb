import {Component, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {UserStoreService} from '../../../../services/store/user-store.service';
import {switchMap, tap} from 'rxjs/operators';
import {SocketLivechatWfirmaMessagesService} from '../../../../services/sockets/socket-livechat-wfirma-messages.service';
import {Subscription, timer} from 'rxjs';
import {ApplicationSettingsService} from '../../../../services/application-settings.service';
import { NgIf } from '@angular/common';
import { AvatarModule } from 'ngx-avatars';
import { MatTooltip } from '@angular/material/tooltip';

@Component({
    selector: 'app-message-typing',
    templateUrl: './message-typing.component.html',
    styleUrls: [
        '../message/message.component.scss',
        './message-typing.component.scss'
    ],
    imports: [NgIf, AvatarModule, MatTooltip]
})
export class MessageTypingComponent implements OnInit, OnDestroy {

    @Input() threadOwnerId: number;
    @Input() clientUserName: string;

    sender;
    userTyping = false;
    subscription: Subscription;
    isReplyingTimeout = this.applicationSettingsService.getValue('CHAT_IS_REPLYING_TIMEOUT');

    constructor(
        public userStoreService: UserStoreService,
        private socketLivechatWfirmaMessagesService: SocketLivechatWfirmaMessagesService,
        private applicationSettingsService: ApplicationSettingsService
    ) {
    }

    private setSocketTypingMessageSubscription() {
        this.subscription = this.socketLivechatWfirmaMessagesService.typingMessage$
            .pipe(
                tap(() => {
                    if (!this.userTyping) {
                        this.userTyping = true;
                    }
                }),
                switchMap(() => timer(this.isReplyingTimeout)),
            )
            .subscribe(() => {
                if (this.userTyping) {
                    this.userTyping = false;
                }
            });
    }

    ngOnInit() {
        this.userStoreService.getUserFromStoreTake1(this.threadOwnerId).subscribe(sender => this.sender = sender);
        this.setSocketTypingMessageSubscription();
    }

    ngOnDestroy() {
        this.subscription?.unsubscribe();
    }

}
