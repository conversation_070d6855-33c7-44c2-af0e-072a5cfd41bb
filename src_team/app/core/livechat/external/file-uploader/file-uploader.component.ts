import {Component, ElementRef, EventEmitter, Input, Output, ViewChild} from '@angular/core';
import { UploaderOptions, UploadFile, UploadInput, UploadOutput, NgxUploaderModule } from 'ngx-uploader';
import {AuthService} from '../../../../services/auth/auth.service';
import {concatMap, take, tap} from 'rxjs/operators';
import {from, Subject} from 'rxjs';
import {environment} from '../../../../../environments/environment';
import {MessagesService} from '../../../../services/livechat-wfirma/messages.service';
import {MessagingService} from '../../../../services/messaging.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import { MatTooltip } from '@angular/material/tooltip';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-file-uploader',
    templateUrl: './file-uploader.component.html',
    styleUrls: ['./file-uploader.component.scss'],
    imports: [NgxUploaderModule, MatTooltip, MatIcon, TranslatePipe]
})
export class FileUploaderComponent {
    @Input() threadId: number;
    @Input() enabled: boolean;

    @Output() refresh = new EventEmitter();

    @ViewChild('fileInput') fileInput: ElementRef;

    uploadComplete$ = new Subject();
    options: UploaderOptions = {
        concurrency: 1,
        maxUploads: 3
    };

    allowedFileContentTypes = [
        'png',
        'jpeg',
        'jpg',
        'zip',
        'xml',
        'csv',
        'txt',
        'pdf',
        'doc',
        'docx',
        'odt',
        'xls',
        'xlsx',
        'rar',
        'ods',
        'rtf',
        '7z',
        'tiff'
    ];

    files: UploadFile[];
    uploadInput: EventEmitter<UploadInput>;

    constructor(
        private authService: AuthService,
        private messagesService: MessagesService,
        private messagingService: MessagingService,
        public translate: TranslateService
    ) {
        this.files = [];
        this.uploadInput = new EventEmitter<UploadInput>();
    }

    private submitFile(messageId: number, file: UploadFile) {
        const headers = this.authService.headers;

        delete headers['Content-Type'];

        const event: UploadInput = {
            type: 'uploadFile',
            url: environment.apiUrl + 'common_file_chat_message/',
            method: 'POST',
            file,
            headers,
            data: {chat_message_id: messageId.toString()}
        };

        this.uploadInput.emit(event);
    }

    private getAddDataObservable() {
        const message = {
            chat_thread_id: this.threadId,
            message: ' ',
            type: 'file',
        };

        return this.messagesService.postMessage(message);
    }

    private uploadStart() {
        from(this.files)
            .pipe(
                take(this.files.length),
                concatMap(file => this.getAddDataObservable().pipe(
                    tap(message => this.submitFile(+message.id, file))
                    )
                )
            )
            .subscribe(
                () => {
                    this.files = [];
                    this.refresh.emit();
                },
                () => this.messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-ATTACHMENT'))
            );
    }

    onUploadOutput(output: UploadOutput): void {
        switch (output.type) {
            case 'allAddedToQueue':
                this.uploadStart();

                break;
            case 'addedToQueue':
                if (typeof output.file !== 'undefined') {

                    const fileNameArr = output.file.name.split('.'),
                        fileExtension = fileNameArr[fileNameArr.length - 1].toLowerCase();

                    if (!(this.allowedFileContentTypes.includes(fileExtension))) {
                        this.messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.WRONG-FILE'));
                        this.refresh.emit();

                        return;
                    }

                    this.files.push(output.file);
                }

                break;
            case 'uploading':
                if (typeof output.file !== 'undefined') {
                    const index = this.files.findIndex(file => typeof output.file !== 'undefined' && file.id === output.file.id);

                    this.files[index] = output.file;
                }
                break;
            case 'removed':
                this.files = this.files.filter((file: UploadFile) => file !== output.file);

                break;
            case 'rejected':
                this.messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.FILE-TOO-LARGE'));
                this.refresh.emit();

                break;
            case 'done':
                this.uploadComplete$.next(null);
                break;
        }
    }

    openSaveFileDialog() {
        this.files = [];
        this.fileInput.nativeElement.click();
    }
}
