<div class="chat-message" [ngClass]="getClasses()">
    <div class="chat-message__text">
        <div
            *ngIf="status !== 'error'; else errorTemplate"
            class="chat-message__file"
            [ngClass]="disabled ? 'disabled' : ''"
            (click)="downloadAttachment()"
        >
            <mat-icon>{{iconType}}</mat-icon>
            <span class="chat-message__name">{{fileName}}</span>
        </div>
    </div>
</div>
<div [ngClass]="getClasses()" class="chat-message chat-message--date">
    <mat-icon *ngIf="message.is_read !== '1'">radio_button_unchecked</mat-icon>
    <mat-icon *ngIf="message.is_read === '1'">check_circle</mat-icon>
    <span class="chat-message__datetime">{{message?.created}}</span>
</div>

<ng-template #errorTemplate>
    <div class="chat-message-error">
        <mat-icon>error</mat-icon>
        <span>{{'LIVECHAT-WFIRMA.ERROR-SENDING' | translate}}</span>
    </div>
</ng-template>
