import {Component, OnChanges} from '@angular/core';
import {<PERSON><PERSON>anitizer} from '@angular/platform-browser';
import saveAs from 'file-saver';
import {UserStoreService} from '../../../../services/store/user-store.service';
import {MessageComponent} from '../message/message.component';
import {AvatarStoreService} from '../../../../services/store/avatar-store.service';
import {IsMobileService} from '../../../../services/is-mobile.service';
import {UserAvatarService} from '../../../../services/user-avatar.service';
import {MessagesService} from '../../../../services/livechat-wfirma/messages.service';
import {MessagingService} from '../../../../services/messaging.service';
import {finalize} from 'rxjs/operators';
import {MatDialog} from '@angular/material/dialog';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import { NgClass, NgIf } from '@angular/common';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-message-file',
    templateUrl: './message-file.component.html',
    styleUrls: [
        '../message/message.component.scss',
        './message-file.component.scss'
    ],
    imports: [NgClass, DefaultClassDirective, NgIf, MatIcon, TranslatePipe]
})
export class MessageFileComponent extends MessageComponent implements OnChanges {

    disabled = false;
    fileId: number;
    fileName: string;
    fileType: string;
    iconType = 'attachment';
    status = '';

    constructor(
        userStoreService: UserStoreService,
        avatarStoreService: AvatarStoreService,
        dialog: MatDialog,
        isMobileService: IsMobileService,
        userAvatarService: UserAvatarService,
        sanitizer: DomSanitizer,
        private messagesService: MessagesService,
        private messagingService: MessagingService,
        public translate: TranslateService
    ) {
        super(userStoreService, avatarStoreService, dialog, isMobileService, userAvatarService, sanitizer);
    }

    ngOnChanges() {
        if (this.message) {
            this.fetchData();
        }
    }

    private setError() {
        this.status = 'error';
    }

    private fetchData() {
        this.messagesService.getMessageAttachment(+this.message.id).subscribe(
            result => {
                try {
                    this.fileName = result[0].CommonFile.filename;
                    this.fileId = result[0].CommonFile.id;
                    this.fileType = result[0].CommonFile.type;
                    this.status = 'sent';
                } catch (e) {
                    this.setError();
                }
            },
            () => this.setError()
        );
    }

    downloadAttachment() {
        this.disabled = true;

        this.messagesService.downloadAttachment(+this.fileId)
            .pipe(finalize(() => this.disabled = false))
            .subscribe(
                result => {
                    try {
                        saveAs(new Blob([result.body], {type: this.fileType}), this.fileName);
                        this.disabled = false;
                    } catch (err) {
                        this.messagingService.logToConsoleAndShowError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-DOWNLOAD'), err);
                    }
                },
                err => {
                    const message = err.status === 403 ? this.translate.instant('LIVECHAT-WFIRMA.SCANNING-FILE') : this.translate.instant('LIVECHAT-WFIRMA.ERROR-DOWNLOAD');

                    this.messagingService.showError(message);
                }
            );
    }
}
