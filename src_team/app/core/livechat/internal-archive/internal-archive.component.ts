import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {SidebarsContainerService} from '../../../services/sidebars-container.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {filter, map, startWith, switchMap} from 'rxjs/operators';
import {combineLatest, forkJoin, of, Subscription} from 'rxjs';
import {ChatInternalThread} from '../../../common/interfaces/chat-internal-thread.interface';
import {ChatInternalUserThread} from '../../../common/interfaces/chat-internal-user-thread.interface';
import {UserService} from '../../../services/user.service';
import {AuthService} from '../../../services/auth/auth.service';
import { ActivatedRoute, NavigationEnd, Router, RouterEvent, RouterLinkActive, RouterLink, RouterOutlet } from '@angular/router';
import {IsMobileService} from '../../../services/is-mobile.service';
import {MessagingService} from '../../../services/messaging.service';
import {MessagesInternalService} from '../../../services/livechat-wfirma/messages-internal.service';
import {ChatInternalMessages} from '../../../common/interfaces/chat-internal-messages.interface';
import {SocketChatInternalMessagesService} from '../../../services/sockets/socket-chat-internal-messages.service';
import {ThreadsInternalArchiveService} from '../../../services/livechat-wfirma/threads-internal-archive.service';
import {UserInterface} from '../../../common/interfaces/user.interface';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective, DefaultLayoutGapDirective } from 'ngx-flexible-layout/flex';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { NgIf, NgFor, DatePipe } from '@angular/common';
import { MatNavList, MatListItem } from '@angular/material/list';
import { AvatarModule } from 'ngx-avatars';
import { EmptyInternalThreadArchiveComponent } from './empty-internal-thread-archive/empty-internal-thread-archive.component';

@Component({
    selector: 'app-internal-archive',
    templateUrl: './internal-archive.component.html',
    styleUrls: ['./internal-archive.component.scss'],
    imports: [DefaultLayoutDirective, DefaultClassDirective, NgIf, MatNavList, NgFor, MatListItem, RouterLinkActive, RouterLink, DefaultLayoutAlignDirective, DefaultLayoutGapDirective, AvatarModule, RouterOutlet, EmptyInternalThreadArchiveComponent, DatePipe, TranslatePipe]
})
export class InternalArchiveComponent implements OnInit, OnDestroy {
    baseUrl = '/internal-archive';
    threadsReady = false;
    selectedThreadVisible = false;
    threadsListVisible = false;
    threads: ChatInternalThread[] = [];
    subscriptions: Subscription = new Subscription();
    isXs: boolean;

    constructor(
        private sidebarsContainerService: SidebarsContainerService,
        public translate: TranslateService,
        private _threadsInternalArchiveService: ThreadsInternalArchiveService,
        private _messagesInternalService: MessagesInternalService,
        private _userService: UserService,
        private _authService: AuthService,
        private router: Router,
        private _isMobileService: IsMobileService,
        private _messagingService: MessagingService,
        private _route: ActivatedRoute,
        private _socketChatInternalMessagesService: SocketChatInternalMessagesService
    ) {
    }

    private setMainSubscription() {
        const currentUrl$ = this.router.events.pipe(
            filter((routerEvent): routerEvent is NavigationEnd => routerEvent instanceof NavigationEnd),
            map(navigationEnd => navigationEnd.url),
            startWith(this.router.url)
        );

        this.subscriptions.add(
            combineLatest([this._isMobileService.isXS, currentUrl$]).subscribe(
                ([isMobile, currentUrl]) => {
                    this.setVisibility(isMobile, currentUrl);
                    this.threadsReady = true;
                },
                () => this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-THREADS'))
            )
        );
    }

    private setVisibility(isXs: boolean, url: string) {
        this.isXs = isXs;

        if (isXs) {
            this.selectedThreadVisible = url !== this.baseUrl;
            this.threadsListVisible = !this.selectedThreadVisible;
        } else {
            this.selectedThreadVisible = true;
            this.threadsListVisible = true;

            if (url === this.baseUrl && this.threads[0]) {
                this.router.navigate(['thread', this.threads[0].id], {relativeTo: this._route});
            }
        }
    }

    ngOnInit() {
        this.getThreadsWithUsers();

        this.subscriptions.add(
            this._threadsInternalArchiveService.refreshThreads$.subscribe(data => {
                const threadToUpdate = this.threads.find(thread => thread.id === data[0]['chat_internal_thread_id']);

                threadToUpdate.lastMessage = data[0];
            })
        );
    }

    getThreadsWithUsers() {
        this._socketChatInternalMessagesService.threads$
            .pipe(
                startWith(true),
                switchMap(() => this._threadsInternalArchiveService.getThreads()
                    .pipe(
                        switchMap(threads => {
                            if (!threads || threads.length === 0) {
                                return of([]);
                            }

                            const threadObservables = threads.map((thread: ChatInternalThread) => {
                                return this._threadsInternalArchiveService.getUserThread(thread.id)
                                    .pipe(
                                        switchMap(usersThread => {
                                            if (usersThread) {
                                                thread.users = [];

                                                const userIds = usersThread.map(ut => ut.user_id);
                                                const userObservables = userIds.map(userId =>
                                                    this._userService.getUser(userId)
                                                );

                                                return forkJoin(userObservables).pipe(
                                                    map((users: UserInterface[]) => {
                                                        thread.users = users;

                                                        return thread;
                                                    })
                                                );
                                            }
                                            return of(thread);
                                        })
                                    );
                            });

                            return forkJoin(threadObservables);
                        })
                    )
                )
            )
            .subscribe(
                updatedThreads => {
                    this.threads = updatedThreads;
                    this.setMainSubscription();
                },
                error => {
                    console.error('Error fetching threads with users and messages:', error);
                }
            );
    }


    ngOnDestroy(): void {
        this.setMainSubscription();
    }
}
