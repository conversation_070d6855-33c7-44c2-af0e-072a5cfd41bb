@use '../../../../../variables' as *;

.chat-message {
    align-items: stretch;
    display: flex;
    padding: 10px 4px 0 4px;

    &.user .chat-message__text {
        margin-right: 10px;
        margin-left: 10px;
    }

    &.user .chat-message__read-icon {
        display: none;
    }

    &__datetime {
        margin-left: 4px;
    }

    &--date {
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-bottom: 12px;
        color: $light-medium-grey;
        padding: 10px;

        mat-icon {
            height: 12px;
            width: 12px;
            min-width: 12px;
            min-height: 12px;
            max-width: 12px;
            max-height: 12px;
            font-size: 12px;
            margin:0;
            padding:0;
        }
    }

    &.user {
        justify-content: flex-start;

        .chat-message__sender {
            order: 0;
            margin-left: 60px;
        }

        .chat-message__text {
            background-color: $light-grey;
        }

        .chat-message--date {
            justify-content: flex-end;
        }

        .chat-message__sender {
            .chat-message__avatar {
                border-radius: 50%;
                height: 40px;
                width: 40px;
                margin-left: 10px;
            }
        }
    }

    &.loggedUser {
        justify-content: flex-end;

        .chat-message__sender {
            order: 1;
            margin-right: 60px;

            .chat-message__avatar {
                border-radius: 50%;
                height: 40px;
                width: 40px;
                margin-left: 10px;
            }
        }
    }

    .chat-message__sender {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
    }

    .chat-message__text {
        border-radius: 10px;
        box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
        color: #333132;
        font-size: 14px;
        line-height: 23px;
        max-width: 600px;
        min-height: 30px;
        padding: 13px;
        width: 100%;
        word-break: break-word;
    }
}

.chat-message-error {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    span {
        margin-left: 8px;
    }
}
