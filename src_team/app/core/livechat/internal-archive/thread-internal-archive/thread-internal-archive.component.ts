import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>estroy, OnInit, ViewChild} from '@angular/core';
import {TranslateService} from '@ngx-translate/core';
import {ChatInternalThread} from '../../../../common/interfaces/chat-internal-thread.interface';
import {catchError, distinctUntilChanged, finalize, map, switchMap, take} from 'rxjs/operators';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {BehaviorSubject, combineLatest, forkJoin, of, Subject, Subscription} from 'rxjs';
import {ChatInternalMessages} from '../../../../common/interfaces/chat-internal-messages.interface';
import {UserService} from '../../../../services/user.service';
import {MessagingService} from '../../../../services/messaging.service';
import {AuthService} from '../../../../services/auth/auth.service';
import {ChatInternalUserThread} from '../../../../common/interfaces/chat-internal-user-thread.interface';
import {IsMobileService} from '../../../../services/is-mobile.service';
import {ApplicationSettingsService} from '../../../../services/application-settings.service';
import {SocketsService} from '../../../../services/sockets/sockets.service';
import {SocketChatInternalMessagesService} from '../../../../services/sockets/socket-chat-internal-messages.service';
import {ThreadsInternalArchiveService} from '../../../../services/livechat-wfirma/threads-internal-archive.service';
import {MessagesInternalArchiveService} from '../../../../services/livechat-wfirma/messages-internal-archive.service';
import {UserInterface} from '../../../../common/interfaces/user.interface';
import { NgIf, NgFor, NgClass, AsyncPipe } from '@angular/common';
import { DefaultClassDirective, DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { MatDrawerContainer, MatDrawerContent } from '@angular/material/sidenav';
import { MatAnchor } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { MessageInternalArchiveComponent } from '../message-internal-archive/message-internal-archive.component';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-thread-internal-archive',
    templateUrl: './thread-internal-archive.component.html',
    styleUrls: ['./thread-internal-archive.component.scss'],
    imports: [NgIf, DefaultClassDirective, MatDrawerContainer, MatDrawerContent, MatAnchor, RouterLink, DefaultShowHideDirective, MatIcon, DefaultLayoutDirective, DefaultLayoutAlignDirective, NgFor, NgClass, MessageInternalArchiveComponent, MatProgressSpinner, AsyncPipe]
})
export class ThreadInternalArchiveComponent implements OnInit, OnDestroy, AfterViewChecked {
    @ViewChild('messageList') private messageList: ElementRef;

    thread: ChatInternalThread;
    messages: ChatInternalMessages[] = [];
    sidebarOpened = false;
    isLoading$ = new BehaviorSubject<boolean>(true);

    public wait = false;

    private loading: false | 'previous' | 'next' = 'next';
    private SCROLL_LOAD_POSITION = 50;
    private doScroll: false | 'top' | 'bottom' = false;
    private CHAT_MESSAGE_PACKET_SIZE: number;
    private lastScrollPosition = Number.MAX_SAFE_INTEGER;
    private firstMessageInDatabaseId: number;
    private subscription: Subscription;
    private subscriptions = new Subscription();
    private messagesFetched$ = new Subject();
    private lastThreadId: number;

    constructor(
        public translate: TranslateService,
        private _route: ActivatedRoute,
        private _threadsInternalArchiveService: ThreadsInternalArchiveService,
        private _messagesInternalArchiveService: MessagesInternalArchiveService,
        private _userService: UserService,
        private _messagingService: MessagingService,
        private _isMobileService: IsMobileService,
        private _authService: AuthService,
        private _applicationSettingsService: ApplicationSettingsService,
        private _socketChatInternalMessagesService: SocketChatInternalMessagesService,
        private _socketsService: SocketsService
    ) {
        this.CHAT_MESSAGE_PACKET_SIZE = this._applicationSettingsService.getValue('CHAT_MESSAGE_PACKET_SIZE');
    }

    ngOnInit() {
        const routeParams$ = this._route.params.pipe(map(params => +params.id));

        this.subscriptions.add(
            routeParams$.subscribe(threadId => {
                this.isLoading$.next(true);
                this.loadMessagesFirstTime(threadId);
            })
        );

        this.subscriptions.add(
            combineLatest([
                routeParams$.pipe(distinctUntilChanged()),
                this._socketsService.connected$,
                this.messagesFetched$.pipe(distinctUntilChanged())
            ]).subscribe(
                ([threadId, socketsConnected, messagesFetched]) => {
                    if (threadId && socketsConnected && messagesFetched) {
                        this.setSocketsSubscription();
                    }
                }
            )
        );
    }

    onScroll(event: any) {
        if (this.loading) {
            return;
        }

        const scrollbarPosition = +event.target.scrollTop,
            scrollUP = this.checkScrollDirectionUp(scrollbarPosition),
            reachedTop = this.reachedTop(scrollbarPosition);

        if (scrollUP && reachedTop) {
            this.loading = 'previous';
            this.loadPreviousMessages();
        }
    }

    private checkScrollDirectionUp(position: number) {
        const result = position < this.lastScrollPosition;

        this.lastScrollPosition = position;

        return result;
    }

    private reachedTop(scrollbarPosition: number) {
        return scrollbarPosition < this.SCROLL_LOAD_POSITION;
    }

    private setScrollPosition() {
        if (this.loading === 'previous') {
            this.lastScrollPosition = 0;

            if (!this.allMessagesFetched) {
                this.doScroll = 'top';
            }
        } else {
            this.lastScrollPosition = Number.MAX_SAFE_INTEGER;
            this.doScroll = 'bottom';
        }

        this.loading = false;
    }

    private scrollToEnableLoadMore() {
        setTimeout(() => this.messageList.nativeElement.scrollTop = 300, 300);
    }

    private scrollToBottom() {
        setTimeout(() => this.messageList.nativeElement.scrollTop = this.messageList.nativeElement.scrollHeight, 500);
    }

    private get allMessagesFetched() {
        return this.messages.length
            ? this.firstMessageInDatabaseId === +this.messages[0].id
            : true;
    }

    private loadPreviousMessages() {
        if (this.allMessagesFetched) {
            return;
        }

        this._messagesInternalArchiveService.getMessages(+this.thread.id, +this.messages[0].id, 'desc', this.CHAT_MESSAGE_PACKET_SIZE).subscribe(
            result => {
                this.setScrollPosition();
                this.addNewMessages(result);
            },
            () => {
                this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-DOWNLOADING'));
            }
        );
    }

    private loadMessagesFirstTime(threadId: number) {
        forkJoin({
            firstMessage: this._messagesInternalArchiveService.getFirstMessageInThreadId(threadId),
            messages: this._messagesInternalArchiveService.getMessages(threadId, null, 'desc', this.CHAT_MESSAGE_PACKET_SIZE),
            thread: this._threadsInternalArchiveService.getThread(threadId),
            userThreads: this._threadsInternalArchiveService.getUserThread(threadId)
        }).pipe(
            switchMap(({ firstMessage, messages, thread, userThreads }) => {
                const threadData = thread[0];
                this.firstMessageInDatabaseId = firstMessage;
                this.thread = threadData;

                const userRequests = userThreads.map(ut =>
                    this._userService.getUser(ut.user_id).pipe(
                        take(1),
                        catchError(err => {
                            console.error(`Failed to load user ${ut.user_id}:`, err);

                            return of(null);
                        })
                    )
                );

                return forkJoin(userRequests).pipe(
                    map((users: UserInterface[]) => ({
                        users: users.filter((u: UserInterface) => u !== null),
                        messages,
                        userThreads
                    }))
                );
            }),
            finalize(() => this.isLoading$.next(false))
        ).subscribe({
            next: ({ users, messages }) => {
                this.thread.users = users;
                this.messages = messages.map(message => ({
                    ...message,
                    user_type: +message.user_id === this._authService.getUserId() ? 'loggedUser' : 'user'
                }));

                this.sortMessages();
                this.messagesFetched$.next(this.thread.id);
                this.setScrollPosition();
                this.setOpenSidebarStatus();
                setTimeout(() => this.wait = true, 500);
            },
            error: () => {
                this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-MESSAGES'));
                this.messages = [];
                this.thread = null;
            }
        });
    }

    private loadRemainingMessages() {
        forkJoin([
            this._messagesInternalArchiveService.getMessages(+this.thread.id, this.lastMessageId, 'asc', 9999),
            this._threadsInternalArchiveService.getUserThread(+this.thread.id)
        ]).pipe(
            finalize(() => this.isLoading$.next(false))
        ).subscribe(([messages, userThreads]) => {
                const otherUserThread = userThreads.find((ut: ChatInternalUserThread) => +ut.user_id !== this._authService.getUserId());

                if (otherUserThread) {
                    this._userService.getUser(otherUserThread.user_id).pipe(
                        take(1),
                        map(user => {
                            this.thread.user = user;
                            return user;
                        })
                    ).subscribe(user => {
                        const newMessages = messages.map(message => ({
                            ...message,
                            user_type: +message.user_id === this._authService.getUserId() ? 'loggedUser' : 'user'
                        }));

                        this.addMessages(newMessages);
                        this._threadsInternalArchiveService.triggerRefreshThreads(newMessages);
                        this.sortMessages();
                        this.scrollToBottom();
                        setTimeout(() => this.wait = true, 500);
                    });
                } else {
                    this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-USER-THREAD'));
                }
            },
            () => {
                this._messagingService.showError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-MESSAGES'));
            });
    }

    private addMessages(messages: ChatInternalMessages[]) {
        for (const message of messages) {
            if (!this.findMessageById(+message.id)) {
                this.messages.push(message);
            }
        }
    }

    private findMessageById(id: number) {
        return this.messages.find(message => +message.id === id);
    }

    private get lastMessageId() {
        const length = this.messages.length;

        return length ? +this.messages[this.messages.length - 1].id : 0;
    }

    private addNewMessages(messages: ChatInternalMessages[]) {
        for (const message of messages) {
            if (+message.chat_internal_thread_id === +this.thread.id && !this.getMessageById(+message.id)) {
                this.messages.push(message);
            }
        }

        this.sortMessages();
    }

    private getMessageById(id: number) {
        return this.messages.find(message => +message.id === id);
    }

    private sortMessages() {
        this.messages = this.messages.sort((a, b) => +a.id - +b.id);
    }

    private setOpenSidebarStatus() {
        this._isMobileService.isLarge.pipe(take(1)).subscribe(isLarge => this.sidebarOpened = isLarge);
    }

    ngOnDestroy() {
        this.subscriptions?.unsubscribe();
    }

    ngAfterViewChecked() {
        if (!this.messages.length || !this.doScroll) {
            return;
        }

        if (this.doScroll === 'bottom') {
            this.scrollToBottom();
        } else if (this.doScroll === 'top') {
            this.scrollToEnableLoadMore();
        }

        this.doScroll = false;
    }

    private setSocketsSubscription() {
        if (this.lastThreadId) {
            this._socketChatInternalMessagesService.leave(this.lastThreadId);
        }

        if (this.subscription) {
            this.subscription?.unsubscribe();
        }

        this._socketChatInternalMessagesService.join(+this.thread.id);

        this.subscription = this._socketChatInternalMessagesService.messages$.subscribe(
            message => {
                if (+message.threadId === +this.thread.id && !this.findMessageById(+message.messageId)) {
                    this.loadRemainingMessages();
                }
            },
            err => this._messagingService.logToConsoleAndShowError(this.translate.instant('LIVECHAT-WFIRMA.ERROR-SOCKET'), err)
        );

        this.lastThreadId = +this.thread.id;
    }

    get isUserBanned() {
        return !!+this.thread?.user?.banned;
    }

}
