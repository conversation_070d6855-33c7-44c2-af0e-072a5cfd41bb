<ng-container *ngIf="!(isLoading$ | async); else loadingMessage">
    <div class="chat-thread" ngClass.xs="xs">
        <mat-drawer-container>
            <mat-drawer-content>
                <ng-container>
                    <div class="thread-header">
                        <a mat-button routerLink="/chat" fxHide.gt-xs>
                            <mat-icon>arrow_back</mat-icon>
                        </a>
                        <div class="thread-info" *ngIf="thread">
                            <span fxLayout fxLayoutAlign="space-between">
                                <ng-container *ngFor="let user of thread.users; let last = last">
                                    {{ user.firstname }} {{ user.lastname }}{{!last ? ', ' : ''}}
                                </ng-container>
                            </span>
                        </div>
                    </div>
                </ng-container>
                <hr class="ruler">
                <div #messageList
                     class="messages-container"
                     [ngClass]="isUserBanned ? 'messages-blocked-list' : 'messages-list'"
                     (scroll)="onScroll($event)">
                    <ng-container *ngFor="let message of messages; let lastMessage = last">
                        <app-message-internal-archive
                                [message]="message"
                        ></app-message-internal-archive>
                    </ng-container>
                </div>
            </mat-drawer-content>
        </mat-drawer-container>
    </div>
</ng-container>

<ng-template #loadingMessage>
    <div class="spinner-wrapper">
        <mat-spinner></mat-spinner>
    </div>
</ng-template>
