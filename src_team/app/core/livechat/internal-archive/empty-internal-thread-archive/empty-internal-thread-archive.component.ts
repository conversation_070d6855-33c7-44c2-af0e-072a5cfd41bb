import { Component, OnInit } from '@angular/core';
import dayjs from 'dayjs';
import { MatDrawerContainer, Mat<PERSON>rawerContent } from '@angular/material/sidenav';
import { <PERSON><PERSON>nch<PERSON>, MatButton } from '@angular/material/button';
import { DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { MatIcon } from '@angular/material/icon';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { NgFor } from '@angular/common';
import { AvatarModule } from 'ngx-avatars';
import { MatTooltip } from '@angular/material/tooltip';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-empty-internal-thread-archive',
    templateUrl: './empty-internal-thread-archive.component.html',
    styleUrls: ['./empty-internal-thread-archive.component.scss'],
    imports: [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ontent, MatAnchor, DefaultShowHideDirective, MatIcon, DefaultLayoutDirective, DefaultLayoutAlignDirective, NgFor, AvatarModule, MatTooltip, MatButton, TranslatePipe]
})
export class EmptyInternalThreadArchiveComponent implements OnInit {

    messages: { text: string; createdAt: string; type: string; author: string; avatarColor: string; }[];

    constructor() {
    }

    ngOnInit(): void {
        const date = dayjs();
        const formatedDate = date.format('YYYY-MM-DD HH:mm');
        // Przykładowe wiadomości
        this.messages = [
            {
                text: `Depart do be so he enough talent. Sociable formerly six but handsome.
                    Up do view time they shot. He concluded disposing provision by questions as situation.
                    Its estimating are motionless day sentiments end. Calling an imagine at forbade.
                    At name no an what like spot. Pressed my by do affixed he studied.`,
                createdAt: formatedDate,
                type: 'user',
                author: 'John Doe',
                avatarColor: '#f8d400',
            },
            {
                text: `Style too own civil out along. Perfectly offending attempted add arranging age gentleman concluded. Get who uncommonly our expression ten increasing considered occasional travelling.
                    Ever read tell year give may men call its.
                    Piqued son turned fat income played end wicket. To do noisy downs round an happy books.`,
                createdAt: formatedDate,
                type: 'employee',
                author: 'Martin Clove',
                avatarColor: '#e54858',
            },
            {
                text: `Depart do be so he enough talent. Sociable formerly six but handsome. Up do view time they shot. He concluded disposing provision by questions as situation.
                    Its estimating are motionless day sentiments end. Calling an imagine at forbade.
                    At name no an what like spot. Pressed my by do affixed he studied.`,
                createdAt: formatedDate,
                type: 'user',
                author: 'John Doe',
                avatarColor: '#f8d400',
            },
            {
                text: `Its sometimes her behaviour are contented. Do listening am eagerness oh objection collected. Together gay feelings continue juvenile had off one.
                    Unknown may service subject her letters one bed. Child years noise ye in forty. 
                    Loud in this in both hold. My entrance me is disposal bachelor remember relation.`,
                createdAt: formatedDate,
                type: 'employee',
                author: 'Martin Clove',
                avatarColor: '#e54858',
            },
        ];
    }

}
