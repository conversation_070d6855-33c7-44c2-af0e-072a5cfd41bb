@use '../../../../../variables' as *;

$input-background-color: #fff;
$toolbar-height: 60px;
$thread-info-height: 55px;

.chat-thread {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    width: 100%;
    z-index: 999;

    mat-drawer-container {
        border-radius: $container-border-radius;
    }

    .backdrop {
        -webkit-backdrop-filter: blur(2.5px);
        backdrop-filter: blur(2.5px);
        background: rgba(246, 248, 250, 0.4);
        height: 100%;
        position: absolute;
        width: 100%;
        z-index: 100;
    }

    .client-data {
        width: 320px;
    }

    .sidebar-toggle {
        position: absolute;
        right: 10px;
        top: 4px;
        z-index: 9999;
    }

    .mat-drawer-container {
        background-color: #fff;
        height: 100%;
    }

    .messages-list {
        height: calc(100% - #{$livechat-wfirma-input-height} - #{$thread-info-height} - 5px);
        padding: 10px;
        width: 100%;
        overflow: hidden;
        overflow-y: auto;
    }

    .thread-header {
        align-items: center;
        display: flex;
        justify-content: space-between;
        min-height: $thread-info-height;
        padding: 0 10px;

        .thread-info {
            display: flex;
            flex-direction: column;
            margin-left: 10px;
            margin-right: 50px;
            width: 100%;

            .client-name {
                color: #1270B6;
                font-size: 16px;
                font-weight: 500;
            }
        }
    }

    .ruler {
        background-color: $grey;
        border-width: 0.5px;
        margin-bottom: 0;
        margin-top: 0;
        opacity: 0.6;
        width: calc(100% - 40px);
    }

    .thread-closed {
        position: relative;

        span {
            background-color: #fff;
            left: 50%;
            padding: 0 10px;
            position: absolute;
            top: 0;
            transform: translate(-50%, -50%);
        }
    }
}

.chat-message {
    align-items: stretch;
    display: flex;
    padding: 10px 4px 0 4px;

    &.user .chat-message__text {
        margin-right: 10px;
        margin-left: 10px;
    }

    &.user .chat-message__read-icon {
        display: none;
    }

    &__datetime {
        margin-left: 4px;
    }

    &--date {
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-bottom: 12px;
        color: $light-medium-grey;
        padding: 10px;

        mat-icon {
            height: 12px;
            width: 12px;
            min-width: 12px;
            min-height: 12px;
            max-width: 12px;
            max-height: 12px;
            font-size: 12px;
            margin:0;
            padding:0;
        }
    }

    &.employee {
        justify-content: flex-end;

        .chat-message__sender {
            order: 2;
            margin-left: 10px;
        }

        .chat-message__text {
            background-color: $light-grey;
        }
    }

    .chat-message__sender {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
    }

    .chat-message__avatar {
        border-radius: 50%;
        height: 30px;
        width: 30px;
    }

    .chat-message__text {
        border-radius: 10px;
        box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
        color: #333132;
        font-size: 14px;
        line-height: 23px;
        max-width: 600px;
        min-height: 30px;
        padding: 13px;
        width: 100%;
        word-break: break-word;
    }
}

.chat-message-error {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    span {
        margin-left: 8px;
    }
}


.chat-input {
    align-items: center;
    background-color: rgba($input-background-color, 0.10);
    display: flex;
    height: $livechat-wfirma-input-height;
    flex-direction: column;
    width: 100%;
}

.textarea {
    -ms-writing-mode: initial;
    -webkit-appearance: textarea;
    -webkit-rtl-ordering: logical;
    -webkit-writing-mode: initial;
    border-width: 15px 0 0 0;
    border-top: 15px solid $light-grey;
    cursor: text;
    display: inline-block;
    flex-grow: 1;
    font-size: 14px;
    letter-spacing: normal;
    line-height: 20px;
    min-width: 0;
    outline: none;
    overflow-wrap: break-word;
    overflow-y: auto;
    padding: 10px 30px 10px 20px;
    resize: none;
    text-align: start;
    text-indent: 0;
    text-rendering: auto;
    text-shadow: none;
    text-transform: none;
    white-space: pre-wrap;
    width: 100%;
    word-spacing: normal;
    writing-mode: initial;
}

.toolbar {
    align-items: center;
    display: flex;
    height: $toolbar-height;
    justify-content: space-between;
    padding: 0 20px;
    width: 100%;
}

