<div class="livechat-wfirma" fxLayout="row" ngClass.xs="xs">
    <mat-nav-list [disableRipple]="true" class="threads-list" *ngIf="threadsListVisible">
        <ng-container *ngIf="threads.length > 0; else emptyThreadList">
            <a
                *ngFor="let thread of threads"
                mat-list-item
                routerLink="thread/{{thread.id}}"
                routerLinkActive="active"
                class="thread">
                <div class="thread-overview">
                    <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="4px">
                        <div fxLayout fxLayoutAlign="start center" fxLayoutGap="10px">
                            <div>
                                <ngx-avatars
                                    data-cy=avatar
                                    [size]="40"
                                    [value]="thread.users.length.toString()"
                                >
                                </ngx-avatars>
                            </div>
                            <div fxLayout="column wrap"  fxLayoutGap="4px">
                                <span class="thread-names">
                                    <ng-container *ngFor="let user of thread.users; let last = last">
                                        {{ user.firstname }} {{ user.lastname }}{{!last ? ', ' : ''}}
                                    </ng-container>
                                </span>
                            </div>
                        </div>
                    </div>
                    <span class="thread-date">{{thread.lastMessage?.modified | date : 'yyyy-MM-dd HH:mm'}}</span>
                </div>
            </a>
        </ng-container>
        <ng-template #emptyThreadList>
            <div class="empty-thread-list" *ngIf="threadsReady">
                <img src="/src_team/../../assets/images/empty-thread-list.svg" alt="empty thread list">
                <div class="text">
                    {{'LIVECHAT-WFIRMA.EMPTY-THREADS' | translate}}<br>{{'LIVECHAT-WFIRMA.EMPTY-THREADS-2' | translate}}
                </div>
            </div>
        </ng-template>
    </mat-nav-list>

    <div class="thread-container" *ngIf="selectedThreadVisible">
        <ng-container *ngIf="threads.length > 0; else emptyThread">
            <router-outlet></router-outlet>
        </ng-container>
    </div>

    <ng-template #emptyThread>
        <app-empty-internal-thread-archive></app-empty-internal-thread-archive>
    </ng-template>
</div>
