<div class="menu-sidebar">
    <div>
        <div class="menu-sidebar-logo"
             [ngClass]="{'menu-sidebar-border': isExpanded}">
            <a routerLink="/">
                <img
                    src="assets/images/logo-big.svg"
                    width="120"
                    height="28"
                    alt="Logo 5 ways..."
                    class="menu-sidebar-logo-big"
                    [ngClass]="{'opacity': !isExpanded}">
                <img
                    src="assets/images/logo.svg"
                    width="27"
                    height="28"
                    alt="Logo 5 ways..."
                    class="menu-sidebar-logo-small"
                    [ngClass]="{'opacity': isExpanded}"
                >
            </a>
            <a (click)="toggle($event)"
               class="menu-sidebar-arrow"
               [ngStyle]="{ color: '#1a9267' }"
               [ngClass]="{'menu-sidebar-rotate': isExpanded}">
                <ng-container>
                    <i-lucide size="16" name="chevron-right"></i-lucide>
                </ng-container>
            </a>
        </div>
    </div>
    <div class="menu-sidebar-position">
        <ng-container *ngFor="let item of (mainRoutes$ | async)">
            <div *ngIf="!item.data.permission || (item.data.permission | checkPermissionName)">
                <a
                    [routerLink]="item.path"
                    [tourAnchor]="getTourAnchor(item)"
                    class="menu-sidebar-item"
                    [ngClass]="{'menu-sidebar-sizing': !isExpanded,  'menu-sidebar-active': getFirstSegment(currentUrl) === getFirstSegment(item.path)}"
                    [matTooltip]="!isExpanded ? (item.data.mainMenuName | translate) : null"
                    matTooltipPosition="after">
                    <div class="menu-sidebar-element">
                        <div class="menu-sidebar-element">
                            <i-lucide [size]="16" [ngStyle]="{ color: '#3a3a3a', height: '16px' }" [name]="item.data.iconName"></i-lucide>
                            <span [ngClass]="{'opacity': !isExpanded}" class="menu-sidebar-link">{{ item.data.mainMenuName | translate }}</span>
                        </div>

                        <div *ngIf="item.data.counter && totalIssueCount > 0"
                             class="menu-sidebar-counter flex-center"
                             [ngClass]="{'collapsed-style': !isExpanded}">
                            <span class="menu-sidebar-number" [ngClass]="{'opacity': !isExpanded}">{{ totalIssueCount }}</span>
                        </div>
                    </div>
                </a>
            </div>
        </ng-container>
    </div>
</div>
