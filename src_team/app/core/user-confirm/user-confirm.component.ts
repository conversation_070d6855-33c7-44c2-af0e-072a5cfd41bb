import {Component, inject, Inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {catchError, switchMap, tap} from 'rxjs/operators';
import {FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {UserService} from '../../services/user.service';
import {AuthService} from '../../services/auth/auth.service';
import {UserStoreService} from '../../services/store/user-store.service';
import {MatFormField, MatLabel} from '@angular/material/select';
import {MatInput} from '@angular/material/input';
import {MatButton} from '@angular/material/button';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';

@Component({
    selector: 'app-user-confirm',
    templateUrl: './user-confirm.component.html',
    styleUrls: ['./user-confirm.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatButton, TranslatePipe]
})
export class UserConfirmComponent {
    form: UntypedFormGroup;

    private alertService: AlertService = inject(AlertService);

    constructor(
        public dialogRef: MatDialogRef<UserConfirmComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        private userService: UserService,
        private authService: AuthService,
        private userStoreService: UserStoreService,
        private translate: TranslateService) {
        this.form = new UntypedFormGroup({
            firstname: new UntypedFormControl('', [Validators.required, Validators.pattern(/^(\s+\S+\s*)*(?!\s).*$/), Validators.maxLength(64)]),
            lastname: new UntypedFormControl('', [Validators.required, Validators.pattern(/^(\s+\S+\s*)*(?!\s).*$/), Validators.maxLength(64)]),
            companyName: new UntypedFormControl('', [Validators.required, Validators.pattern(/^(\s+\S+\s*)*(?!\s).*$/), Validators.maxLength(128)])
        });
    }

    onSubmit() {
        const user = {
            'firstname': this.form.get('firstname').value,
            'lastname': this.form.get('lastname').value,
            'company_name': this.form.get('companyName').value,
        };

        this.userService.editUser(this.authService.getUserId(), user)
            .pipe(
                tap(() => this.userStoreService.refreshUserStore()),
                switchMap(() => {
                    return this.userService.addCompany(user.company_name);
                }),
                // @ts-ignore
                catchError(error => {
                    if (error) {
                        this.alertService.showAlert(this.translate.instant('USER-CONFIRM.ERROR'), AlertType.ERROR, AlertDuration.MEDIUM);
                    }
                })
            )
            .subscribe(data => {
                if (data) {
                    this.alertService.showAlert(this.translate.instant('USER-CONFIRM.SUCCESS'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                    this.dialogRef.close();
                }
            });
    }
}
