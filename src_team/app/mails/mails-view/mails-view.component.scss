@use '../../../variables' as *;

.container {
    display: flex;
    flex-direction: row;
    background-color: $fiveways-white;
    height: 100%;
    padding: 0;
}

.toolbar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 94%;
    margin-top: 15px;
    height: 48px;
    border: 1px solid $fiveways-stroke-2;
    border-radius: 8px;
    padding: 8px;
}

.issue-view-detailed {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 91%;
    overflow: hidden;
    z-index: 1;

    .header {
        width: 100%;
        min-height: 64px;
        border-radius: 4px;
        overflow: hidden;
        flex-shrink: 0;

        .accordion-item {
            border-bottom: 1px solid $fiveways-stroke-2;
        }

        .accordion-header {
            width: 100%;
            padding: 24px;
            text-align: left;
            background-color: $fiveways-white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;

            &__left {
                display: flex;
                align-items: center;
                gap: 10px;
                font-family: Manrope;
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                letter-spacing: 0.32px;
                color: $fiveways-gray;
            }

            &__right {
                display: flex;
                align-items: center;
                gap: 8px;
                font-family: Manrope;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 0.24px;
                color: $fiveways-gray;

                .icon {
                    transition: transform 0.3s ease;
                }
            }

            &.open {
                .icon {
                    transform: rotate(180deg);
                }
            }
        }

        .accordion-content {
            display: flex;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, padding 0.3s ease-out;
            background-color: $fiveways-header-bg;
            padding: 0 24px;

            p {
                margin: 15px 0;
            }

            &.open {
                max-height: 620px;
                padding: 24px;
            }

            &__left {
                display: flex;
                flex-direction: column;
                gap: 8px;

                &__item {
                    display: flex;
                    flex-direction: row;
                    align-items: center;

                    &__title {
                        font-family: Manrope;
                        font-size: 11px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: normal;
                        letter-spacing: 0.22px;
                    }

                    &__description {
                        margin-left: 8px;
                        font-family: Manrope;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: normal;
                        letter-spacing: 0.24px;
                    }
                }
            }
        }
    }

    .body {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;
        min-height: 0;
        padding: 24px;
        overflow: auto;
        padding-bottom: calc(80px + 24px);

        &__header {
            width: 100%;
            height: 22px;
            display: flex;
            flex-direction: row;
            gap: 8px;
            border-left: 2px solid $fiveways-warning;
            font-family: Manrope;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: 0.32px;
            padding-left: 8px;
            color: $fiveways-gray;

            &__border {
                top: 6px;
                position: relative;
                border-bottom: 1px solid $fiveways-stroke-2;
            }

            &__id {
                font-family: Manrope;
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                letter-spacing: 0.32px;
                color: $fiveways-gray;
                cursor: pointer;
            }

            .vertical-separator {
                width: 1px;
                height: 100%;
                background-color: $fiveways-stroke-2;
            }

            &__title {
                font-family: Manrope;
                font-size: 14px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                letter-spacing: 0.28px;
            }
        }

        .horizontal-separator {
            width: 100%;
            height: 1px;
            background-color: $fiveways-stroke-2;
        }

        .body {
            display: flex;
            flex-direction: column;
            height: 100%;
            align-items: center;
            position: relative;
            overflow-y: auto;

            &::-webkit-scrollbar {
                width: 8px;
                z-index: 999;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
                z-index: 999;
            }

            &::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 4px;
                z-index: 999;
            }

            &__content {
                display: flex;
                flex-direction: column;
                height: 100%;
                font-family: Manrope;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 0.16px;
                color: $fiveways-gray;
                gap: 16px;

                &__item {
                    display: flex;
                    flex-direction: column;
                    gap: 16px;

                    &__header {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;

                        &__right {
                            padding-right: 2px;
                        }
                    }

                    &__center {
                        margin-bottom: 16px;
                    }
                }
            }

            &__editor {
                flex: 1;
                min-height: 0;
                overflow: auto;

                &__toolbar {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 48px;
                    border: 1px solid $fiveways-stroke-2;
                    border-radius: 8px;
                    padding: 8px;

                }
            }
        }
    }

    .footer {
        position: absolute;
        bottom: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 50%;
        height: 80px;
        padding: 24px;
        background-color: $fiveways-white;
        z-index: 10;

        &__left {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }

        &__right {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }
    }
}

.issue-mainblock-wrapper {
    position: relative;
    width: 100%;
    height: 100%;

    .block-text {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        height: fit-content;
        width: fit-content;
        color: #fff;
        z-index: 5;
        font-family: Manrope;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: 0.28px;
        pointer-events: all;

        .take-button {
            display: flex;
            justify-content: center;
            margin-top: 4px;
        }
    }

    &:is(.blocked) {
        pointer-events: none;
    }

    &.blocked {
        &::after {  // Użyj pseudo-elementu
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.6);
            pointer-events: none;
            z-index: 9999;
        }
    }
}

.issue-view-simple {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;
    box-shadow: -5px 0px 8px 0px $fiveways-shadow;
    z-index: 2;

    .header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 24px;
        width: 100%;
        min-height: 64px;
        overflow: hidden;
        flex-shrink: 0;
        border-bottom: 1px solid #DBDFF7;

        .accordion-item {
            border-bottom: 1px solid $fiveways-stroke-2;
        }

        .accordion-header {
            width: 95%;
            padding: 18px 24px 18px 24px;
            text-align: left;
            background-color: $fiveways-white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;

            &__left {
                display: flex;
                align-items: center;
                gap: 19px;
                font-family: Manrope;
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                letter-spacing: 0.32px;
                color: $fiveways-gray;
            }

            &__right {
                display: flex;
                align-items: center;
                gap: 8px;
                font-family: Manrope;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 0.24px;
                color: $fiveways-gray;

                .icon {
                    transition: transform 0.3s ease;
                    color: $fiveways-button-text;
                    cursor: pointer;
                }
            }

            &.open {
                .icon {
                    transform: rotate(180deg);
                }
            }
        }

        .accordion-content {
            display: flex;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, padding 0.3s ease-out;
            background-color: $fiveways-header-bg;
            padding: 0 24px;

            p {
                margin: 15px 0;
            }

            &.open {
                max-height: 620px;
                padding: 24px;
            }

            &__left {
                display: flex;
                flex-direction: column;
                gap: 8px;

                &__item {
                    display: flex;
                    flex-direction: row;
                    align-items: center;

                    &__title {
                        font-family: Manrope;
                        font-size: 11px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: normal;
                        letter-spacing: 0.22px;
                    }

                    &__description {
                        margin-left: 8px;
                        font-family: Manrope;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: normal;
                        letter-spacing: 0.24px;
                    }
                }
            }
        }
    }

    .body {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;
        height: 100%;
        padding: 24px;
        overflow: auto;
        padding-bottom: calc(80px + 24px);
        align-items: center;

        &__content {
            display: flex;
            flex-direction: column;
            min-height: 100px;
            max-height: 250px;
            overflow-y: auto;
            font-family: Manrope;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: 0.16px;
            color: $fiveways-gray;
        }

        &__editor {
            width: 100%;
            height: 90%;
            overflow: auto;
        }
    }
}

.editor {
    height: 100%;
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

.cursor-pointer {
    cursor: pointer;
}
