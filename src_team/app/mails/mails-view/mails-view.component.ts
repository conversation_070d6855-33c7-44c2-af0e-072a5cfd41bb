import { LucideAngularModule, icons } from 'lucide-angular';
import {ChangeDetectorRef, Component, inject, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {forkJoin, Observable, Subscription} from 'rxjs';
import {IssueService} from '../../services/issue.service';
import {ActivatedRoute, Router} from '@angular/router';
import {SidebarsContainerService} from '../../services/sidebars-container.service';
import {AuthService} from '../../services/auth/auth.service';
import {IssueActionAllowedService} from '../../services/issue-action-allowed.service';
import {IsMobileService} from '../../services/is-mobile.service';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {catchError, delay, filter, map, switchMap, switchMapTo, take, tap} from 'rxjs/operators';
import {EmailService} from '../../services/email.service';
import {EmailMessageInterface} from '../../common/interfaces/email-message-interface';
import {HttpErrorResponse} from '@angular/common/http';
import {ConfirmDialogComponent} from '../../shared/confirm-dialog/confirm-dialog.component';
import {MatDialog} from '@angular/material/dialog';
import {SimpleMenuItem} from '../../common/interfaces/simple-menu.interface';
import {UserProfileDialogComponent} from '../../users-sidebar/user-profile-dialog/user-profile-dialog.component';
import {UserService} from '../../services/user.service';
import {ApplicationTourService} from '../../services/application-tour.service';
import {DialogService} from '../../services/dialog-details.service';
import {MailAttachmentsService} from '../../services/mail-attachments.service';
import {DatePipe, NgClass, NgIf} from '@angular/common';
import {DefaultClassDirective} from 'ngx-flexible-layout/extended';
import {ClipboardModule} from 'ngx-clipboard';
import {MatTooltip} from '@angular/material/tooltip';
import {ShadowWrapperComponent} from '../../shared/shadow-wrapper/shadow-wrapper.component';
import {MailsBodyComponent} from '../mails-body/mails-body.component';
import {MailsAttachedFilesListComponent} from '../mails-attached-files-list/mails-attached-files-list.component';
import {DownloadButtonComponent} from '../../shared/download-button/download-button.component';
import {TagsComponent} from '../../shared/tags/tags.component';
import {MoreOptionsComponent} from '../../shared/more-options/more-options.component';
import {UserAvatarDisplayComponent} from '../../shared/user-avatar-display/user-avatar-display.component';
import {ButtonComponent} from '../../elements/button/button.component';
import {MailsEditorComponent} from '../mails-editor/mails-editor.component';
import {TimestampToDatePipe} from '../../shared/pipes/timestamp-to-date.pipe';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';
import {AlertService} from '../../services/alert.service';
import { ObjectNameType } from '../../interfaces/object-name-type';

@Component({
    selector: 'app-mails-view',
    templateUrl: './mails-view.component.html',
    styleUrls: ['./mails-view.component.scss'],
    imports: [NgClass, DefaultClassDirective, NgIf, ClipboardModule, MatTooltip, ShadowWrapperComponent, MailsBodyComponent, MailsAttachedFilesListComponent, DownloadButtonComponent, TagsComponent, MoreOptionsComponent, UserAvatarDisplayComponent, ButtonComponent, MailsEditorComponent, DatePipe, TranslatePipe, TimestampToDatePipe, LucideAngularModule]
})
export class MailsViewComponent implements OnInit, OnDestroy {
    @Input()
    tagsEditMode = true;

    message: EmailMessageInterface;
    mailData: EmailMessageInterface;

    showSidebar = true;
    isHandset$: Observable<boolean>;
    isHandsetTablet$: Observable<boolean>;
    subscriptions = new Subscription();
    userId: number;
    editorExpanded = false;
    editorFullExpanded = false;
    buttonExpand = true;
    editorVisible = false;
    entryList: string;
    forwardEditorVisible: boolean;
    setIsReadSubscription: Subscription;

    mailId: string;
    editorClass = 'medium';
    isOpenAccordion: boolean = false;
    isEditorOpen: boolean = true;
    tagObjectName: ObjectNameType = 'mail_sent_mysql_message';
    targetPath: string;
    moreOptionsMenu: SimpleMenuItem[];
    user;
    mailReceiver;
    priority: number;
    commonFileId: string = null;
    commonFileIdArr: string[];
    commonFileName: string;
    commonFileType: string;
    commonFileAttachments: { commonFileId: string, commonFileName: string, commonFileType: string }[] = [];

    private alertService: AlertService = inject(AlertService);

    constructor(
        private issueService: IssueService,
        private route: ActivatedRoute,
        private sidebarsContainerService: SidebarsContainerService,
        private authService: AuthService,
        public issueActionAllowedService: IssueActionAllowedService,
        private isMobileService: IsMobileService,
        public translate: TranslateService,
        private emailService: EmailService,
        private router: Router,
        private dialog: MatDialog,
        private userService: UserService,
        private applicationTourService: ApplicationTourService,
        private dialogService: DialogService,
        private mailAttachmentService: MailAttachmentsService,
        private cdr: ChangeDetectorRef
    ) {
        this.isHandset$ = this.isMobileService.isMobileView;
        this.isHandsetTablet$ = this.isMobileService.isTabletView;

        this.sidebarsContainerService.sidebar('users').compress().drawerPush(false);
        this.userId = this.authService.getUserId();
    }

    private setExpandedEditorSubscription() {
        this.subscriptions.add(
            this.issueService.expandedEditor.asObservable().pipe(
                tap(expanded => this.editorExpanded = expanded),
                delay(150)
            ).subscribe(
                expanded => this.buttonExpand = !expanded
            )
        );
    }

    private setExpandedFullEditorSubscription() {
        this.subscriptions.add(
            this.issueService.expandedFullEditor.asObservable().pipe(
                tap(expanded => this.editorFullExpanded = expanded),
                delay(150)
            ).subscribe(
                expanded => this.buttonExpand = !expanded
            )
        );
    }

    ngOnInit() {
        this.setExpandedEditorSubscription();
        this.setExpandedFullEditorSubscription();
        this.collapseEditor();
        this.getMailData();
        this.updateMenuItems();

        this.route.queryParams.subscribe(params => {
            this.tagObjectName = params['tagObjectName'];
            this.targetPath = params['targetPath'];
        });

        this.applicationTourService.initTour();
        this.subscriptions.add(
            this.emailService.openForwardEditor$.subscribe(isOpen => {
                this.editorVisible = isOpen;
                this.editorClass = isOpen ? 'full' : 'medium';
                this.forwardEditorVisible = isOpen;
            })
        );

        this.userService.getUser(this.userId).subscribe(user => {
            this.user = user;
        });
    }

    sidebarToggle() {
        this.showSidebar = !this.showSidebar;
    }

    getMailData() {
        this.mailId = this.route.snapshot.paramMap.get('id');

        this.emailService.getEmailById(+this.mailId).pipe(
            catchError(() => {
                return this.emailService.getSentEmailById(this.mailId);
            }),
            take(1)
        ).subscribe(mailData => {
            this.mailData = mailData;
            this.getMailReceiver(this.mailData.user_id);
            this.priority = this.mailData.priority;
            this.setIsReadSubscription = this.emailService.setIsRead(+this.mailId).subscribe();

            if (this.mailId) {
                this.getCommonFileIdAndAttachmentData(+this.mailId);
            }
        });
    }

    collapseEditor() {
        this.issueService.setExpandedEditor(false);
        this.editorVisible = false;
        this.forwardEditorVisible = false;
        this.editorClass = 'medium';
    }

    toggleMobileEditor() {
        this.issueService.toggleExpandedEditor();
    }

    reply() {
        this.editorVisible = true;
        this.editorClass = 'full';
    }

    clickAddPriority() {
        this.emailService.setPriority(this.mailData.id).subscribe(
            () => {
                this.mailData.priority = 1;
            },
            (httpErrorResponse: HttpErrorResponse) => {
                console.error(httpErrorResponse.error.errorMessages);
            }
        );

        this.priority = 1;
    }

    clickDelPriority() {
        this.emailService.unsetPriority(this.mailData.id).subscribe(
            () => {
                this.mailData.priority = 0;
            },
            (httpErrorResponse: HttpErrorResponse) => {
                console.error(httpErrorResponse.error.errorMessages);
            }
        );

        this.priority = 0;
    }

    clickBack() {
        this.router.navigateByUrl('mail/' + (this.entryList ? this.entryList : 'important'));
    }

    clickRemove() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '450px',
                data: {
                    header: this.translate.instant('MAILS-VIEW.REMOVE-CONFIRM')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result));

        confirm$.pipe(
            switchMapTo(this.targetPath === 'draft' ? this.emailService.deleteDraft(+this.mailData.id) : this.emailService.remove(this.mailData.id))
        ).subscribe(
            () => {
                this.alertService.showAlert(this.translate.instant('MAILS-VIEW.REMOVE-INFO'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.clickBack();
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.alertService.showAlert(this.translate.instant('MAILS-VIEW.REMOVE-ERROR'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                console.error(httpErrorResponse.error.errorMessages);
            }
        );
    }

    ngOnDestroy() {
        this.subscriptions?.unsubscribe();
        this.setIsReadSubscription?.unsubscribe();
    }

    toggleAccordion() {
        this.isOpenAccordion = !this.isOpenAccordion;
    }

    openEditor() {
        this.isEditorOpen = !this.isEditorOpen;
        this.forwardEditorVisible = !this.forwardEditorVisible;
    }

    openForwardEditor() {
        this.forwardEditorVisible = !this.forwardEditorVisible;
        this.isEditorOpen = !this.isEditorOpen;
    }

    updateMenuItems(): void {
        this.moreOptionsMenu = [
            {
                label: this.priority
                    ? this.translate.instant('MAILS-VIEW.DELETE-PRIORITY')
                    : this.translate.instant('MAILS-VIEW.SET-PRIORITY'),
                action: () => this.togglePriority()
            },
            { label: this.translate.instant('MAILS-VIEW.TRASH'), action: () => this.clickRemove() }
        ];
    }

    close() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            this.router.navigate(['/mail/list/important']);
        }
    }

    openUser(userId: number) {
        this.dialog.open(UserProfileDialogComponent, {
            width: '690px',
            height: '620px',
            data: { userId: userId },
            autoFocus: false,
            panelClass: 'full-width-dialog'
        });
    }

    togglePriority() {

        +this.priority ? this.clickDelPriority() : this.clickAddPriority();

        this.updateMenuItems();
    }

    isMultipleValue(el: string[]) {
        return el?.length > 1;
    }

    private getMailReceiver(id): void {
        this.userService.getUser(id).subscribe(value => {
            this.mailReceiver = value.email;
        });
    }

    private getCommonFileIdAndAttachmentData(id: number) {
        this.mailAttachmentService.getReceivedAttachments(id)
            .pipe(
                switchMap(ids => {

                    if (!this.isMultipleValue(ids)) {
                        this.commonFileId = ids[0];
                        return this.processAttachmentData(this.commonFileId)
                            .pipe(
                                map(data => [data])
                            );
                    } else {
                        this.commonFileIdArr = ids;
                        return forkJoin(this.commonFileIdArr.map(fileId => this.processAttachmentData(fileId)));
                    }
                }),
                take(1)
            )
            .subscribe(attachments => {
                this.commonFileAttachments = attachments;

                if (attachments.length > 0) {
                    this.commonFileName = attachments[0].commonFileName;
                    this.commonFileId = attachments[0].commonFileId;
                    this.commonFileType = attachments[0].commonFileType;
                }
                this.cdr.detectChanges();
            });
    }

    private processAttachmentData(fileId: string) {
        return this.mailAttachmentService.getReceivedAttachmentsData(+fileId)
            .pipe(
                map(data => ({
                    commonFileId: fileId,
                    commonFileName: data[0]?.fileName,
                    commonFileType: data[0]?.type
                }))
            );
    }
}
