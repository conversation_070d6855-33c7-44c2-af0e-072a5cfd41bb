import {Component, inject, Inject, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {Subject, Subscription} from 'rxjs';
import {debounceTime} from 'rxjs/operators';
import {COMMA, ENTER, SPACE} from '@angular/cdk/keycodes';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {IssueService} from '../../services/issue.service';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {ClientsService} from '../../services/clients.service';
import {EmailService} from '../../services/email.service';
import {QueueStatus, ServerProtocol} from '../../common/enums/email-integration.enum';
import {MessageData} from '../../common/interfaces/message-data.interface';
import {EmailMessageInterface} from '../../common/interfaces/email-message-interface';
import {EmailIntegrationService} from '../../services/email-integration.service';
import {MailsIntegrationModalComponent} from '../mails-integration-modal/mails-integration-modal.component';
import {quillConfig} from '../../common/configs/quill-config';
import { Store } from '@ngrx/store';
import { selectMailServerState } from '../../store/mail-server/mail-server.selectors';
import {MatFormField, MatLabel} from '@angular/material/select';
import {AsyncPipe, NgClass, NgIf} from '@angular/common';
import {MatIcon} from '@angular/material/icon';
import {MatInput} from '@angular/material/input';
import {DefaultFlexDirective, DefaultLayoutAlignDirective, DefaultLayoutDirective, FlexFillDirective} from 'ngx-flexible-layout/flex';
import {DefaultClassDirective} from 'ngx-flexible-layout/extended';
import {QuillEditorComponent} from 'ngx-quill';
import {TemplateFileUploaderComponent} from '../../shared/template-file-uploader/template-file-uploader.component';
import {MatButton} from '@angular/material/button';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';
import {ButtonVariant} from '../../common/enums/button-variant.enum';
import {MailRecipientComponent} from '../mails-recipient/mail-recipient.component';
import {LucideAngularModule} from 'lucide-angular';
import {DialogService} from '../../services/dialog-details.service';

@Component({
    selector: 'app-send-message-dialog',
    templateUrl: './send-message-dialog.component.html',
    styleUrls: ['./send-message-dialog.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, NgIf, MatIcon, MatInput, DefaultLayoutDirective, DefaultLayoutAlignDirective, FlexFillDirective, DefaultFlexDirective, NgClass, DefaultClassDirective, QuillEditorComponent, TemplateFileUploaderComponent, MatButton, AsyncPipe, TranslatePipe, MailRecipientComponent, MailRecipientComponent, LucideAngularModule],
    providers: [
        {
            provide: MatDialogRef,
            useValue: {}
        },
        {
            provide: MAT_DIALOG_DATA,
            useValue: {}
        }
    ]
})
export class SendMessageDialogComponent implements OnInit, OnDestroy {
    @Input() data: any;

    constructor(
        private fb: FormBuilder,
        private issueService: IssueService,
        public dialog: MatDialog,
        public translate: TranslateService,
        private clientService: ClientsService,
        private emailService: EmailService,
        private emailIntegrationService: EmailIntegrationService,
        public dialogRef: MatDialogRef<SendMessageDialogComponent>,
        private store: Store,
        private dialogService: DialogService
    ) {
        this.initializeData();
    }

    get formCtrlTitle() {
        return this.sendMessageForm.get('title');
    }

    get formCtrlSenders() {
        return this.sendMessageForm?.get('senders');
    }

    get formCtrlCcSenders() {
        return this.sendMessageForm?.get('ccSenders');
    }

    get formCtrlBccSenders() {
        return this.sendMessageForm?.get('bccSenders');
    }

    get formCtrlMessage() {
        return this.sendMessageForm.get('message');
    }

    get isEditMode() {
        return !!this.draftId;
    }

    sendMessageForm: FormGroup;
    allClients: string[] = [];
    Quill;
    editorModules = quillConfig.modules;
    editorFormats = quillConfig.formats;

    expandedEditor$ = this.issueService.expandedEditor;
    separatorKeysCodes: number[] = [ENTER, COMMA, SPACE];
    mailServerId: number;
    initialSelected: string[] = [];
    draftMessage?: EmailMessageInterface;
    senders: string[] = [];
    ccSenders: string[] = [];
    title: string = '';
    message: string = '';
    bodyFooter: string = '';
    draftId: number = null;
    mailData;
    isSendButtonDisabled: boolean = false;
    showSenders: boolean = false;
    showBcc: boolean = false;
    showCc: boolean = false;

    private contentChangeSubject = new Subject<any>();
    private readonly DEBOUNCE_TIME = 3000;
    private contentChangeSubscription: Subscription;
    private sendersChangeSub: Subscription;
    private isCreatingDraft = false;
    private subscriptions: Subscription = new Subscription();
    private alertService: AlertService = inject(AlertService);

    protected readonly ButtonVariant = ButtonVariant;

    ngOnInit(): void {
        this.initSendMessageForm();
        this.getClientsData();
        this.getMailServerId();
        this.getFooterData();

        this.contentChangeSubscription = this.contentChangeSubject.pipe(
            debounceTime(this.DEBOUNCE_TIME)
        ).subscribe(() => {
            this.handleContentChange();
        });
    }

    ngOnDestroy(): void {
        if (this.draftId) {
            this.handleContentChange();
        }

        const messageData: MessageData = {
            senders: this.senders,
            ccSenders: this.ccSenders,
            title: this.sendMessageForm.get('title')?.value || '',
            message: this.sendMessageForm.get('message')?.value || '',
            bodyFooter: this.bodyFooter
        };

        this.emailService.setMessageData(messageData);
        this.contentChangeSubscription?.unsubscribe();
        this.sendersChangeSub?.unsubscribe();
        this.subscriptions?.unsubscribe();
    }

    getClientsData() {
        const conditions = '?page=1&limit=9999';

        this.clientService.getClients(conditions).subscribe((response) => {
            this.allClients = response.items.map(item => `${item.email}`.trim());
            this.showSenders = true;
        });
    }

    onEditorCreated(instance) {
        this.Quill = instance;
    }

    onSelectionChanged() {
        if (this.Quill.hasFocus()) {
            this.issueService.setExpandedEditor(true);
        }
    }

    onChange() {
        this.mailData = {
            MailSentMysqlMessage: {
                mail_server_id: this.mailServerId,
                mail_address: Array.isArray(this.formCtrlSenders.value)
                    ? this.formCtrlSenders.value.join(',')
                    : this.formCtrlSenders.value,
                mail_to_name: Array.isArray(this.formCtrlSenders.value)
                    ? this.formCtrlSenders.value.join(',')
                    : this.formCtrlSenders.value,
                cc_mail_addresses: Array.isArray(this.formCtrlCcSenders.value)
                    ? this.formCtrlCcSenders.value.join(',')
                    : this.formCtrlCcSenders.value,
                bcc_mail_addresses: Array.isArray(this.formCtrlBccSenders.value)
                    ? this.formCtrlBccSenders.value.join(',')
                    : this.formCtrlBccSenders.value,
                mail_subject: this.formCtrlTitle.value,
                mail_body: this.Quill.root.innerHTML,
                status: QueueStatus.DRAFT
            }
        };

        if (this.draftId) {
            this.contentChangeSubject.next(null);
        } else {
            if (!this.isCreatingDraft) {
                this.isCreatingDraft = true;
                this.emailService.createDraftMail(this.mailData).subscribe(
                    response => {
                        this.draftId = response['id'];
                        this.isCreatingDraft = false;
                        this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-CREATED'), AlertType.SUCCESS);
                    },
                    error => {
                        this.isCreatingDraft = false;
                        this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-ERROR'), AlertType.ERROR);
                    }
                );
            }
        }
    }

    onSubmit() {
        this.isSendButtonDisabled = true;
        if (this.sendMessageForm.valid) {
            this.contentChangeSubscription?.unsubscribe();
            this.sendersChangeSub?.unsubscribe();

            this.emailIntegrationService.verifyMailServerTokenStatus().subscribe((isVerified) => {
                if (!isVerified) {
                    this.dialog.open(MailsIntegrationModalComponent, {
                        width: '480px',
                        disableClose: true
                    });
                    return;
                }

                const mailData = {
                    MailSend: {
                        mail_id: this.draftId,
                        mail_server_id: this.mailServerId,
                        mail_to_address: Array.isArray(this.formCtrlSenders.value)
                            ? this.formCtrlSenders.value.join(',')
                            : this.formCtrlSenders.value,
                        cc_mail_addresses: Array.isArray(this.formCtrlCcSenders.value)
                            ? this.formCtrlCcSenders.value.join(',')
                            : this.formCtrlCcSenders.value,
                        bcc_mail_addresses: Array.isArray(this.formCtrlBccSenders.value)
                            ? this.formCtrlBccSenders.value.join(',')
                            : this.formCtrlBccSenders.value,
                        mail_subject: this.formCtrlTitle.value,
                        mail_to_name: this.formCtrlSenders.value,
                        mail_body: this.Quill.root.innerHTML,
                        status: QueueStatus.NEW
                    }
                };

                this.emailService.sendMail(mailData).subscribe(
                    response => {
                        this.dialogService.closeDialog();
                        this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.SUCCESSFULLY-SENT'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                        this.clearMessageData();
                        this.draftId = null;
                    },
                    error => {
                        this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.ERROR-SENT'), AlertType.ERROR, AlertDuration.MEDIUM);
                    }
                );
            });
        }
    }

    closeDialog() {
        this.dialogRef.close();
    }

    clearMessageData(): void {
        this.emailService.setMessageData({senders: [], title: '', message: '', bodyFooter: ''});

        this.sendMessageForm.reset({
            senders: [],
            ccSenders: [],
            bccSenders: [],
            title: '',
            message: ''
        });
    }

    initSendMessageForm() {
        this.sendMessageForm = this.fb.group({
            senders: [this.data?.selectedEmails || [], [Validators.required]],
            ccSenders: [this.senders],
            bccSenders: [this.senders],
            title: [this.title || ''],
            message: [this.message || '']
        });
    }

    handleContentChange() {
        if (this.draftId) {
            this.emailService.updateDraftMail(this.mailData, this.draftId).subscribe(
                response => {
                    this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-UPDATED'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                },
                error => {
                    this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-ERROR'), AlertType.ERROR, AlertDuration.MEDIUM);
                }
            );
        }
    }

    private getMailServerId() {
        this.subscriptions.add(
            this.store.select(selectMailServerState).subscribe({
                next: (mailServerState) => {
                    const protocolName = mailServerState?.server_protocol_name;

                    if (!protocolName) {
                        return;
                    }

                    let serverProtocol: ServerProtocol | undefined;

                    if (protocolName === 'SMTP' || protocolName === 'IMAP') {
                        serverProtocol = ServerProtocol.SMTP;
                    }

                    this.subscriptions.add(
                        this.emailService.getMailServer(serverProtocol).subscribe(
                            response => {
                                const mailServerId = response['results'][0]?.MailServer?.id;

                                if (mailServerId) {
                                    this.mailServerId = mailServerId;
                                }
                            },
                            error => {
                                console.error('Error fetching mail server:', error);
                            }
                        )
                    );
                }
            })
        );
    }

    private initializeData() {
        if (!this?.draftMessage && this?.draftMessage) {
            return;
        }

        if (this.initialSelected) {
            this.senders = this.initialSelected.filter(email => email && email.trim());
        } else if (this.draftMessage) {
            if (this.draftMessage.mail_address) {
                this.senders = this.draftMessage.mail_address.split(',').map(email => email.trim());
            }
            this.title = this.draftMessage.mail_subject || '';
            this.message = this.draftMessage.mail_body || '';
            this.draftId = +this.draftMessage.id;
        }
    }

    private getFooterData() {
        this.emailService.getFooter().subscribe((data) => {
            this.bodyFooter = data.body ?? '';

            if (this.bodyFooter !== null ) {
                const horizontalRule = '<br><br>';
                this.message += horizontalRule + this.bodyFooter;
            }

            this.formCtrlMessage.setValue(this.message);
        });
    }

    toggleCc(event: MouseEvent) {
        event.stopPropagation();

        this.showCc = !this.showCc;
    }

    toggleBcc(event: MouseEvent) {
        event.stopPropagation();

        this.showBcc = !this.showBcc;
    }
}
