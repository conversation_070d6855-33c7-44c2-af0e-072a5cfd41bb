<mat-form-field [formGroup]="group" class="full-width mt-6 mb-0" [floatLabel]="senders.length ? 'always' : 'auto'">
    <mat-label>{{label}}</mat-label>

    <mat-chip-grid
        #chipList
        aria-label="Address selection"
        (focusin)="hasMailsInputFocus = true"
        (focusout)="handleMailsInputBlur($event)"
    >
        <ng-container *ngIf="hasMailsInputFocus">
            <mat-chip
                    *ngFor="let sender of senders"
                    [removable]="removable"
            >
                {{ sender }}
                <mat-icon
                        *ngIf="removable"
                        matChipRemove
                        (mousedown)="remove(sender, $event)"
                >
                    cancel
                </mat-icon>
            </mat-chip>
        </ng-container>

        <ng-container *ngIf="!hasMailsInputFocus">
            <mat-chip
                    *ngFor="let sender of visibleSenders"
                    [removable]="false"
            >
                {{ sender }}
            </mat-chip>
            <mat-chip *ngIf="senders.length > maxVisibleSenders">
                {{ 'SEND-MESSAGE-DIALOG.PLUS-EMAILS' | translate }} {{ senders.length - maxVisibleSenders }}
            </mat-chip>
        </ng-container>

        <input
                matInput
                #sendersInput
                [formControl]="formCtrlSenders"
                [matAutocomplete]="autoComponent"
                [matChipInputFor]="chipList"
                [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                (matChipInputTokenEnd)="add($event)"
                (blur)="handleInputBlur($event)"
                (click)="autocompleteTrigger.openPanel()"
        />
    </mat-chip-grid>

    <mat-autocomplete #autoComponent="matAutocomplete" (optionSelected)="selected($event)">
        <mat-option *ngFor="let sender of filteredSenders | async" [value]="sender">
            {{ sender }}
        </mat-option>
    </mat-autocomplete>
</mat-form-field>
