<div class="messages-container mail-data-body-container" #messagesContainer>
    <ng-container>
        <mat-card class="mat-elevation-z issue-view-list">
            <mat-card-header>
                <div class="header-container">
                    <div class="title-section">
                        <mat-card-title>
                            {{this.mailData?.mail_name}}
                        </mat-card-title>
                        <mat-card-subtitle>
                            {{this.mailRecivedDate | dateRespondoFormat}}
                        </mat-card-subtitle>
                    </div>
                    <div class="action-dropdown">
                        <mat-icon class="action-icon"
                            [matMenuTriggerFor]="menu"
                            #menuTrigger="matMenuTrigger"
                            (click)="$event.stopPropagation()">
                            more_vert
                        </mat-icon>

                        <mat-menu #menu="matMenu" xPosition="before">
                            <button mat-menu-item (click)="openForwardEditor()"><mat-icon>turn_right</mat-icon>{{ 'MAIL-FORWARD-EDITOR.FORWARD' | translate }}</button>
                        </mat-menu>
                    </div>
                </div>

            </mat-card-header>
            <mat-card-content>
                <ng-container>
                    <div class="mail-data-body">
                        <app-shadow-wrapper>
                            <app-mails-body [body]="this.mailData?.mail_body"></app-mails-body>
                        </app-shadow-wrapper>
                    </div>
                </ng-container>
                <ng-container *ngIf="isMultipleValue(commonFileIdArr); else oneFileTemplate">
                    <app-mails-attached-files-list [commonFileObject]="commonFileAttachments"></app-mails-attached-files-list>
                </ng-container>
                <ng-template #oneFileTemplate>
                    <ng-container *ngIf="commonFileId">
                        <app-download-button
                            [fileName]="commonFileName"
                            [fileId]="commonFileId"
                            [fileType]="commonFileType">
                        </app-download-button>
                    </ng-container>
                </ng-template>
            </mat-card-content>
        </mat-card>
    </ng-container>
</div>
