#!/usr/bin/env bash

cd `dirname $0`

git update-index --skip-worktree src_team/environments/environment.ts
git update-index --skip-worktree src_team/environments/environment.prod.ts

git update-index --skip-worktree src_respondo-client/environments/environment.ts
git update-index --skip-worktree src_respondo-client/environments/environment.prod.ts

if ! [ -f /etc/systemd/system/respondo-ui.service ]; then
  cat >/etc/systemd/system/respondo-ui.service <<EOL
  [Unit]
  Description=respondo-ui
  After=redis.service
  [Service]
  User=ubuntu
  Group=ubuntu
  WorkingDirectory=/var/www/respondo-ui
  ExecStart=/var/www/respondo-ui/daemon.sh
  Restart=on-failure
  [Install]
  WantedBy=multi-user.target
EOL
fi